import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService } from 'ngx-webstorage';
import { of, Subject, throwError } from 'rxjs';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { CLIENT_DEVICE, DEMO_DEVICE, TEST_DEVICE } from '../../../app.constants';
import { ListingPageReloadSubjectParameter } from '../../../model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from '../../../model/Country/CountryListResponse.model';
import { DeviceService } from '../../../shared/device.service';
import { ProductStatusEnum } from '../../../shared/enum/Common/ProductStatus.enum';
import { ExceptionHandlingService } from '../../../shared/ExceptionHandling.service';
import { CountryCacheService } from '../../../shared/Service/CacheService/countrycache.service';
import { DeviceOperationService } from '../DeviceService/Device-Operation/device-operation.service';
import { SalesOrderApiCallService } from '../../../shared/Service/SalesOrderService/sales-order-api-call.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { KeyValueMappingServiceService } from '../../../shared/util/key-value-mapping-service.service';
import { MultiSelectDropDownSettingService } from '../../../shared/util/multi-select-drop-down-setting.service';
import { DeviceFilterComponent } from './device-filter.component';

describe('DeviceFilterComponent', () => {
  let component: DeviceFilterComponent;
  let fixture: ComponentFixture<DeviceFilterComponent>;
  let deviceFilterService: jasmine.SpyObj<DeviceOperationService>;
  let deviceService: jasmine.SpyObj<DeviceService>;
  let salesOrderApiCallService: jasmine.SpyObj<SalesOrderApiCallService>;
  let countryCacheService: jasmine.SpyObj<CountryCacheService>;
  let commonsService: jasmine.SpyObj<CommonsService>;

  let exceptionService: jasmine.SpyObj<ExceptionHandlingService>;
  let multiSelectService: jasmine.SpyObj<MultiSelectDropDownSettingService>;
  let keyValueService: jasmine.SpyObj<KeyValueMappingServiceService>;
  let refreshSubject: Subject<ListingPageReloadSubjectParameter>;

  beforeEach(async () => {
    refreshSubject = new Subject<ListingPageReloadSubjectParameter>();

    const deviceFilterServiceSpy = jasmine.createSpyObj('DeviceOperationService', [
      'getDeviceListRefreshSubject',
      'getPackageVersionList',
      'getSalesOrderNumberList',
      'getCountryList',
      'setPackageVersionList',
      'setSalesOrderNumberList',
      'setCountryList',
      'callDeviceListFilterRequestParameterSubject',
      'processFilterSearch',
      'buildDeviceSearchRequest',
      'validateFilterForm',
      'clearAllFiltersAndRefresh'
    ]);
    const deviceServiceSpy = jasmine.createSpyObj('DeviceService', ['getpackageVersion']);
    const salesOrderApiCallServiceSpy = jasmine.createSpyObj('SalesOrderApiCallService', ['getSalesOrderNumberList']);
    const countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache']);
    const toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['info']);
    const exceptionServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    const commonsServiceSpy = jasmine.createSpyObj('CommonsService', [
      'checkForNull',
      'checkNullFieldValue',
      'checkValueIsNullOrEmpty',
      'getEnumMappingSelectedValue',
      'getIdsFromArray',
      'getSelectedValueFromEnum',
      'getSelectedValueFromBooleanKeyValueMapping',
      'getDeviceTypeStringToEnum'
    ]);
    const commonOperationsServiceSpy = jasmine.createSpyObj('CommonOperationsService', ['showEmptyFilterTosteMessge']);
    const multiSelectServiceSpy = jasmine.createSpyObj('MultiSelectDropDownSettingService', [
      'getLockStateDropdownSetting',
      'getEditStateDropdownSetting',
      'getSystemSoftwearVersionDropdownSetting',
      'getDeviceConnectionStateDropdownSetting',
      'getDeviceTypeDropdownSetting',
      'getSalesOrderNumberDrpSetting',
      'getCountryDrpSetting',
      'getProductStatusDrpSetting',
      'setOtherOptionDisabled',
      'setAllOptionEnable'
    ]);
    const keyValueServiceSpy = jasmine.createSpyObj('KeyValueMappingServiceService', [
      'enumOptionToList',
      'lockedUnlockOptionList',
      'editEnableDisableOptionList'
    ]);

    // Setup default return values
    deviceFilterServiceSpy.getDeviceListRefreshSubject.and.returnValue(refreshSubject);
    deviceFilterServiceSpy.getPackageVersionList.and.returnValue([]);
    deviceFilterServiceSpy.getSalesOrderNumberList.and.returnValue([]);
    deviceFilterServiceSpy.getCountryList.and.returnValue([]);

    await TestBed.configureTestingModule({
      declarations: [DeviceFilterComponent],
      imports: [ReactiveFormsModule],
      providers: [
        FormBuilder,
        CommonOperationsService,
        RoleApiCallService,
        ConfirmDialogService,
        PermissionService,
        LocalStorageService,
        HidePermissionNamePipe,
        { provide: DeviceOperationService, useValue: deviceFilterServiceSpy },
        { provide: DeviceService, useValue: deviceServiceSpy },
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: ExceptionHandlingService, useValue: exceptionServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: CommonOperationsService, useValue: commonOperationsServiceSpy },
        { provide: MultiSelectDropDownSettingService, useValue: multiSelectServiceSpy },
        { provide: KeyValueMappingServiceService, useValue: keyValueServiceSpy },
        commonsProviders(null)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DeviceFilterComponent);
    component = fixture.componentInstance;
    deviceFilterService = TestBed.inject(DeviceOperationService) as jasmine.SpyObj<DeviceOperationService>;
    deviceService = TestBed.inject(DeviceService) as jasmine.SpyObj<DeviceService>;
    salesOrderApiCallService = TestBed.inject(SalesOrderApiCallService) as jasmine.SpyObj<SalesOrderApiCallService>;
    countryCacheService = TestBed.inject(CountryCacheService) as jasmine.SpyObj<CountryCacheService>;
    commonsService = TestBed.inject(CommonsService) as jasmine.SpyObj<CommonsService>;

    exceptionService = TestBed.inject(ExceptionHandlingService) as jasmine.SpyObj<ExceptionHandlingService>;
    multiSelectService = TestBed.inject(MultiSelectDropDownSettingService) as jasmine.SpyObj<MultiSelectDropDownSettingService>;
    keyValueService = TestBed.inject(KeyValueMappingServiceService) as jasmine.SpyObj<KeyValueMappingServiceService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize form controls', () => {
      expect(component.filterForm).toBeDefined();
      expect(component.filterForm.get('packageVersions')).toBeDefined();
      expect(component.filterForm.get('connectionState')).toBeDefined();
      expect(component.filterForm.get('deviceLockState')).toBeDefined();
      expect(component.filterForm.get('deviceEditState')).toBeDefined();
      expect(component.filterForm.get('countries')).toBeDefined();
      expect(component.filterForm.get('drpDeviceType')).toBeDefined();
      expect(component.filterForm.get('salesOrderNumber')).toBeDefined();
      expect(component.filterForm.get('productStatus')).toBeDefined();
      expect(component.filterForm.get('deviceId')).toBeDefined();
      expect(component.filterForm.get('deviceSerialNo')).toBeDefined();
      expect(component.filterForm.get('customerName')).toBeDefined();
    });

    it('should initialize dropdown settings', () => {
      // Setup
      const mockSettings = { singleSelection: false, text: 'Select' };
      multiSelectService.getLockStateDropdownSetting.and.returnValue(mockSettings as any);
      multiSelectService.getEditStateDropdownSetting.and.returnValue(mockSettings as any);
      multiSelectService.getSystemSoftwearVersionDropdownSetting.and.returnValue(mockSettings as any);
      multiSelectService.getDeviceConnectionStateDropdownSetting.and.returnValue(mockSettings as any);
      multiSelectService.getDeviceTypeDropdownSetting.and.returnValue(mockSettings as any);
      multiSelectService.getSalesOrderNumberDrpSetting.and.returnValue(mockSettings as any);
      multiSelectService.getCountryDrpSetting.and.returnValue(mockSettings as any);
      multiSelectService.getProductStatusDrpSetting.and.returnValue(mockSettings as any);

      // Call
      component.ngOnInit();

      // Verify
      expect(multiSelectService.getLockStateDropdownSetting).toHaveBeenCalled();
      expect(multiSelectService.getEditStateDropdownSetting).toHaveBeenCalled();
      expect(multiSelectService.getSystemSoftwearVersionDropdownSetting).toHaveBeenCalled();
      expect(multiSelectService.getDeviceConnectionStateDropdownSetting).toHaveBeenCalled();
      expect(multiSelectService.getDeviceTypeDropdownSetting).toHaveBeenCalled();
      expect(multiSelectService.getSalesOrderNumberDrpSetting).toHaveBeenCalled();
      expect(multiSelectService.getCountryDrpSetting).toHaveBeenCalled();
      expect(multiSelectService.getProductStatusDrpSetting).toHaveBeenCalled();
    });

    it('should initialize dropdown data', () => {
      // Setup
      const mockEnumList = [{ key: 'ENABLED', value: 'Enable' }];
      const mockBooleanList = [{ key: 'Yes', value: true }];
      keyValueService.enumOptionToList.and.returnValue(mockEnumList);
      keyValueService.lockedUnlockOptionList.and.returnValue(mockBooleanList);
      keyValueService.editEnableDisableOptionList.and.returnValue(mockBooleanList);

      // Call
      component.ngOnInit();

      // Verify
      expect(keyValueService.enumOptionToList).toHaveBeenCalledTimes(2);
      expect(keyValueService.lockedUnlockOptionList).toHaveBeenCalled();
      expect(keyValueService.editEnableDisableOptionList).toHaveBeenCalled();
      expect(component.deviceTypes).toContain(CLIENT_DEVICE);
      expect(component.deviceTypes).toContain(TEST_DEVICE);
      expect(component.deviceTypes).toContain(DEMO_DEVICE);
    });

    it('should subscribe to device list refresh subject', () => {
      // Setup
      const mockParam = new ListingPageReloadSubjectParameter(true, true, true, false);
      spyOn(component, 'clearFilter');

      // Call
      component.onInitSubject();
      refreshSubject.next(mockParam);

      // Verify
      expect(component.clearFilter).toHaveBeenCalledWith(mockParam);
    });


  });

  describe('Data Loading', () => {
    it('should use cached data when cache is not empty', async () => {
      // Setup cached data
      const cachedPackageVersions = ['v1.0.0', 'v2.0.0'];
      const cachedSalesOrders = ['SO001', 'SO002'];
      const cachedCountries: CountryListResponse[] = [{ id: 1, country: 'USA', languages: ['English'] }];

      deviceFilterService.getPackageVersionList.and.returnValue(cachedPackageVersions);
      deviceFilterService.getSalesOrderNumberList.and.returnValue(cachedSalesOrders);
      deviceFilterService.getCountryList.and.returnValue(cachedCountries);
      spyOn(component as any, 'setFilterValue');

      await component.getInitCall();

      // Verify cached data is used
      expect(component.packageVersionsList).toEqual(cachedPackageVersions);
      expect(component.salesOrderNumberList).toEqual(cachedSalesOrders);
      expect(component.countryList).toEqual(cachedCountries);
      expect(component['setFilterValue']).toHaveBeenCalled();

      // Verify API calls are not made
      expect(deviceService.getpackageVersion).not.toHaveBeenCalled();
      expect(salesOrderApiCallService.getSalesOrderNumberList).not.toHaveBeenCalled();
      expect(countryCacheService.getCountryListFromCache).not.toHaveBeenCalled();
    });

    it('should make API calls when cache is empty', async () => {
      // Setup empty cache
      deviceFilterService.getPackageVersionList.and.returnValue([]);
      deviceFilterService.getSalesOrderNumberList.and.returnValue([]);
      deviceFilterService.getCountryList.and.returnValue([]);

      // Setup API responses
      const apiPackageVersions = ['v1.0.0'];
      const apiSalesOrders = ['SO001'];
      const apiCountries: CountryListResponse[] = [{ id: 1, country: 'USA', languages: ['English'] }];

      deviceService.getpackageVersion.and.returnValue(of({ body: apiPackageVersions } as any));
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(apiSalesOrders));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve(apiCountries));
      commonsService.checkForNull.and.returnValue(apiPackageVersions);
      spyOn(component as any, 'setFilterValue');

      await component.getInitCall();

      // Verify API calls are made
      expect(deviceService.getpackageVersion).toHaveBeenCalled();
      expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
      expect(countryCacheService.getCountryListFromCache).toHaveBeenCalledWith(true);
      expect(component['setFilterValue']).toHaveBeenCalled();
    });

    it('should handle API errors gracefully', async () => {
      // Setup empty cache
      deviceFilterService.getPackageVersionList.and.returnValue([]);
      deviceFilterService.getSalesOrderNumberList.and.returnValue([]);
      deviceFilterService.getCountryList.and.returnValue([]);

      // Setup API error responses
      deviceService.getpackageVersion.and.returnValue(throwError(() => new Error('API error')));
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve([]));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([]));
      spyOn(component as any, 'setFilterValue');

      await component.getInitCall();

      // Verify error handler was called
      expect(exceptionService.customErrorMessage).toHaveBeenCalled();
      expect(component['setFilterValue']).toHaveBeenCalled();
    });

    it('should handle mixed cache scenarios', async () => {
      // Setup mixed cache - some have data, some don't
      deviceFilterService.getPackageVersionList.and.returnValue(['v1.0.0']); // Has data
      deviceFilterService.getSalesOrderNumberList.and.returnValue([]); // Empty
      deviceFilterService.getCountryList.and.returnValue([{ id: 1, country: 'USA', languages: ['English'] }]); // Has data

      // Setup API responses for empty caches
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
      spyOn(component as any, 'setFilterValue');

      await component.getInitCall();

      // Verify mixed behavior
      expect(component.packageVersionsList).toEqual(['v1.0.0']); // From cache
      expect(component.salesOrderNumberList).toEqual(['SO001']); // From API
      expect(component.countryList).toEqual([{ id: 1, country: 'USA', languages: ['English'] }]); // From cache

      // Verify only empty cache API is called
      expect(deviceService.getpackageVersion).not.toHaveBeenCalled();
      expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
      expect(countryCacheService.getCountryListFromCache).not.toHaveBeenCalled();
      expect(component['setFilterValue']).toHaveBeenCalled();
    });
  });



  describe('Country Selection', () => {
    it('should handle country selection with special item', () => {
      // Setup
      const specialItem = { id: -1, country: 'Other' };
      const mockCountryList = [
        { id: 1, country: 'USA', languages: ['English'] },
        { id: 2, country: 'Canada', languages: ['English', 'French'] }
      ];
      component.countryList = mockCountryList;
      multiSelectService.setOtherOptionDisabled.and.returnValue(mockCountryList);

      // Call
      component.onCountrySelect(specialItem);

      // Verify
      expect(component.filterForm.get('countries')?.value).toEqual([specialItem]);
      expect(multiSelectService.setOtherOptionDisabled).toHaveBeenCalledWith(mockCountryList);
    });

    it('should handle country selection with regular item', () => {
      // Setup
      const regularItem = { id: 1, country: 'USA' };

      // Call
      component.onCountrySelect(regularItem);

      // Verify special handling is not triggered
      expect(multiSelectService.setOtherOptionDisabled).not.toHaveBeenCalled();
    });

    it('should handle country deselection', () => {
      // Setup
      const mockCountryList = [
        { id: 1, country: 'USA', languages: ['English'] },
        { id: 2, country: 'Canada', languages: ['English', 'French'] }
      ];
      component.countryList = mockCountryList;
      multiSelectService.setAllOptionEnable.and.returnValue(mockCountryList);

      // Call
      component.onCountryDeSelect();

      // Verify
      expect(multiSelectService.setAllOptionEnable).toHaveBeenCalledWith(mockCountryList);
    });
  });
  describe('Component Lifecycle', () => {
    it('should unsubscribe on destroy', () => {
      // Setup
      component.subscriptionForRefreshList = jasmine.createSpyObj('Subscription', ['unsubscribe']);

      // Call
      component.ngOnDestroy();

      // Verify
      expect(component.subscriptionForRefreshList.unsubscribe).toHaveBeenCalled();
    });

    it('should not throw error if subscription is null on destroy', () => {
      // Setup
      component.subscriptionForRefreshList = null;

      // Call & Verify (no error)
      expect(() => component.ngOnDestroy()).not.toThrow();
    });

    it('should initialize with API calls when isFilterComponentInitWithApicall is true', () => {
      // Setup
      component.isFilterComponentInitWithApicall = true;
      spyOn(component, 'clearFilter');

      // Call
      component.ngOnInit();

      // Verify
      expect(component.clearFilter).toHaveBeenCalled();
    });

    it('should not clear filter when isFilterComponentInitWithApicall is false', () => {
      // Setup
      component.isFilterComponentInitWithApicall = false;
      spyOn(component, 'clearFilter');

      // Call
      component.ngOnInit();

      // Verify
      expect(component.clearFilter).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {


    it('should validate deviceId field', () => {
      // Setup
      const deviceIdControl = component.filterForm.get('deviceId');

      // Test valid input
      deviceIdControl.setValue('valid-id');
      expect(deviceIdControl.valid).toBeTruthy();

      // Test too long input
      const longString = 'a'.repeat(51); // Assuming Small_TextBoxMaxLength is 50
      deviceIdControl.setValue(longString);
      expect(deviceIdControl.hasError('maxlength')).toBeTruthy();
    });

    it('should validate deviceSerialNo field', () => {
      // Setup
      const serialNoControl = component.filterForm.get('deviceSerialNo');

      // Test valid input
      serialNoControl.setValue('SN12345');
      expect(serialNoControl.valid).toBeTruthy();

      // Test invalid pattern (single quote not allowed)
      serialNoControl.setValue("Invalid'SerialNo");
      expect(serialNoControl.hasError('pattern')).toBeTruthy();
    });

    it('should validate customerName field', () => {
      // Setup
      const customerNameControl = component.filterForm.get('customerName');

      // Test valid input
      customerNameControl.setValue('Valid Customer Name');
      expect(customerNameControl.valid).toBeTruthy();

      // Test too long input
      const longString = 'a'.repeat(256); // Assuming TextBoxMaxLength is 255
      customerNameControl.setValue(longString);
      expect(customerNameControl.hasError('maxlength')).toBeTruthy();

      // Test invalid pattern (single quote not allowed)
      customerNameControl.setValue("Invalid'Customer'Name");
      expect(customerNameControl.hasError('pattern')).toBeTruthy();
    });
  });

  describe('setFilterValue Method', () => {
    beforeEach(() => {
      // Setup mock data
      component.countryList = [
        { id: 1, country: 'USA', languages: ['English'] },
        { id: 2, country: 'Canada', languages: ['English', 'French'] }
      ];
      component.lockUnlockStateList = [
        { key: 'Locked', value: true },
        { key: 'Unlocked', value: false }
      ];
      component.editStateList = [
        { key: 'Enabled', value: true },
        { key: 'Disabled', value: false }
      ];
    });

    it('should set filter values when deviceSearchRequestBody is provided', () => {
      // Setup
      const mockSearchRequest = {
        packageVersions: ['v1.0.0', 'v2.0.0'],
        status: 'CONNECTED',
        deviceLockStatus: true,
        isEditable: false,
        countryIds: [1, 2],
        productStatus: [ProductStatusEnum.ENABLED],
        deviceType: 'CLIENT',
        salesOrderNumbers: ['SO001', 'SO002'],
        deviceId: 'DEV123',
        deviceSerialNo: 'SN123',
        customerName: 'Test Customer'
      };
      component.deviceSearchRequestBody = mockSearchRequest;

      commonsService.getEnumMappingSelectedValue.and.returnValue([{ key: 'CONNECTED', value: 'CONNECTED' }]);

      // Call
      component['setFilterValue']();

      // Verify form values are set
      expect(component.filterForm.get('packageVersions')?.value).toEqual(['v1.0.0', 'v2.0.0']);
      expect(component.filterForm.get('deviceId')?.value).toBe('DEV123');
      expect(component.filterForm.get('deviceSerialNo')?.value).toBe('SN123');
      expect(component.filterForm.get('customerName')?.value).toBe('Test Customer');
      expect(component.filterForm.get('salesOrderNumber')?.value).toEqual(['SO001', 'SO002']);
    });

    it('should handle null deviceSearchRequestBody', () => {
      // Setup
      component.deviceSearchRequestBody = null;
      component.listPageRefreshForbackToDetailPage = false;

      // Call
      component['setFilterValue']();

      // Verify no errors and form remains unchanged
      expect(component.filterForm.get('deviceId')?.value).toBe('');
    });

    it('should call deviceListPageRefresh when listPageRefreshForbackToDetailPage is true', () => {
      // Setup
      component.deviceSearchRequestBody = null;
      component.listPageRefreshForbackToDetailPage = true;
      spyOn(component as any, 'deviceListPageRefresh');

      // Call
      component['setFilterValue']();

      // Verify
      expect(component['deviceListPageRefresh']).toHaveBeenCalledWith(component.defaultListingPageReloadSubjectParameter);
    });

    it('should handle empty arrays and null values in deviceSearchRequestBody', () => {
      // Setup
      const mockSearchRequest = {
        packageVersions: null,
        status: null,
        deviceLockStatus: null,
        isEditable: null,
        countryIds: null,
        productStatus: null,
        deviceType: null,
        salesOrderNumbers: null,
        deviceId: null,
        deviceSerialNo: null,
        customerName: null
      } as any;
      component.deviceSearchRequestBody = mockSearchRequest;

      commonsService.getEnumMappingSelectedValue.and.returnValue([]);

      // Call
      component['setFilterValue']();

      // Verify default values are set
      expect(component.filterForm.get('packageVersions')?.value).toEqual([]);
      expect(component.filterForm.get('deviceId')?.value).toBe('');
      expect(component.filterForm.get('deviceSerialNo')?.value).toBe('');
      expect(component.filterForm.get('customerName')?.value).toBe('');
    });
  });

  describe('searchData Method', () => {
    it('should process search when form is valid', () => {
      // Setup
      const mockFormValue = {
        deviceId: 'DEV123',
        deviceSerialNo: 'SN123',
        customerName: 'Test Customer'
      };
      component.filterForm.patchValue(mockFormValue);

      commonsService.checkNullFieldValue.and.returnValue('processed_value');
      deviceFilterService.processFilterSearch.and.returnValue(true);

      // Call
      component.searchData();

      // Verify
      expect(commonsService.checkNullFieldValue).toHaveBeenCalledTimes(3);
      expect(deviceFilterService.processFilterSearch).toHaveBeenCalledWith(
        jasmine.any(Object),
        false,
        component.defaultListingPageReloadSubjectParameter
      );
    });

    it('should return early when processFilterSearch returns false', () => {
      // Setup
      const mockFormValue = {
        deviceId: 'DEV123',
        deviceSerialNo: 'SN123',
        customerName: 'Test Customer'
      };
      component.filterForm.patchValue(mockFormValue);

      commonsService.checkNullFieldValue.and.returnValue('processed_value');
      deviceFilterService.processFilterSearch.and.returnValue(false);

      // Call
      component.searchData();

      // Verify
      expect(deviceFilterService.processFilterSearch).toHaveBeenCalled();
      // Method should return early, no further processing
    });

    it('should handle form validation errors', () => {
      // Setup - make form invalid
      component.filterForm.get('deviceId')?.setValue('a'.repeat(51)); // Exceeds max length

      commonsService.checkNullFieldValue.and.returnValue('processed_value');
      deviceFilterService.processFilterSearch.and.returnValue(true);

      // Call
      component.searchData();

      // Verify
      expect(deviceFilterService.processFilterSearch).toHaveBeenCalledWith(
        jasmine.any(Object),
        false,
        component.defaultListingPageReloadSubjectParameter
      );
    });
  });

  describe('clearFilter Method', () => {
    it('should clear all filters and refresh with default parameter', () => {
      // Setup
      spyOn(component as any, 'clearAllFilter');

      // Call
      component.clearFilter();

      // Verify
      expect(component['clearAllFilter']).toHaveBeenCalled();
      expect(deviceFilterService.clearAllFiltersAndRefresh).toHaveBeenCalledWith(
        component.defaultListingPageReloadSubjectParameter
      );
    });

    it('should clear all filters and refresh with provided parameter', () => {
      // Setup
      const customParam = new ListingPageReloadSubjectParameter(false, false, true, true);
      spyOn(component as any, 'clearAllFilter');

      // Call
      component.clearFilter(customParam);

      // Verify
      expect(component['clearAllFilter']).toHaveBeenCalled();
      expect(deviceFilterService.clearAllFiltersAndRefresh).toHaveBeenCalledWith(customParam);
    });
  });

  describe('clearAllFilter Method', () => {
    it('should reset form and call onCountryDeSelect', () => {
      // Setup
      spyOn(component, 'onCountryDeSelect');
      component.filterForm.patchValue({
        deviceId: 'test',
        deviceSerialNo: 'test',
        customerName: 'test'
      });

      // Call
      component['clearAllFilter']();

      // Verify
      expect(component.onCountryDeSelect).toHaveBeenCalled();
      expect(component.filterForm.get('deviceId')?.value).toBeNull();
      expect(component.filterForm.get('deviceSerialNo')?.value).toBeNull();
      expect(component.filterForm.get('customerName')?.value).toBeNull();
      expect(component.filterForm.get('packageVersions')?.value).toEqual([]);
      expect(component.filterForm.get('salesOrderNumber')?.value).toEqual([]);
      expect(component.filterForm.get('productStatus')?.value).toEqual([]);
    });
  });

  describe('deviceListPageRefresh Method', () => {
    it('should reset form when invalid and call service methods', () => {
      // Setup
      component.filterForm.get('deviceId')?.setValue('a'.repeat(51)); // Make form invalid
      const mockDeviceSearchRequest = { deviceId: 'test' };
      const mockParam = new ListingPageReloadSubjectParameter(true, true, false, false);

      deviceFilterService.buildDeviceSearchRequest.and.returnValue(mockDeviceSearchRequest as any);

      // Call
      component['deviceListPageRefresh'](mockParam);

      // Verify
      expect(deviceFilterService.buildDeviceSearchRequest).toHaveBeenCalledWith(jasmine.any(Object));
      expect(deviceFilterService.callDeviceListFilterRequestParameterSubject).toHaveBeenCalled();
    });

    it('should not reset form when valid', () => {
      // Setup
      const originalFormValue = component.filterForm.value;
      const mockDeviceSearchRequest = { deviceId: 'test' };
      const mockParam = new ListingPageReloadSubjectParameter(true, true, false, false);

      deviceFilterService.buildDeviceSearchRequest.and.returnValue(mockDeviceSearchRequest as any);

      // Call
      component['deviceListPageRefresh'](mockParam);

      // Verify form was not reset
      expect(component.filterForm.value).toEqual(originalFormValue);
      expect(deviceFilterService.buildDeviceSearchRequest).toHaveBeenCalled();
      expect(deviceFilterService.callDeviceListFilterRequestParameterSubject).toHaveBeenCalled();
    });
  });

  describe('Subject Subscription Scenarios', () => {
    it('should handle subscription with isReloadData true and isClearFilter false', () => {
      // Setup
      const mockParam = new ListingPageReloadSubjectParameter(true, false, false, false);
      spyOn(component as any, 'deviceListPageRefresh');

      // Call
      component.onInitSubject();
      refreshSubject.next(mockParam);

      // Verify
      expect(component['deviceListPageRefresh']).toHaveBeenCalledWith(mockParam);
    });

    it('should not process when isReloadData is false', () => {
      // Setup
      const mockParam = new ListingPageReloadSubjectParameter(false, true, false, false);
      spyOn(component, 'clearFilter');
      spyOn(component as any, 'deviceListPageRefresh');

      // Call
      component.onInitSubject();
      refreshSubject.next(mockParam);

      // Verify
      expect(component.clearFilter).not.toHaveBeenCalled();
      expect(component['deviceListPageRefresh']).not.toHaveBeenCalled();
    });

    it('should handle null deviceOperationService gracefully', () => {
      // Setup - temporarily set service to null to test optional chaining
      const originalService = (component as any).deviceOperationService;
      (component as any).deviceOperationService = null;

      // Call & Verify (should not throw error)
      expect(() => component.onInitSubject()).not.toThrow();
      expect(component.subscriptionForRefreshList).toBeUndefined();

      // Restore original service
      (component as any).deviceOperationService = originalService;
    });

    it('should handle null getDeviceListRefreshSubject gracefully', () => {
      // Setup - mock service to return null
      deviceFilterService.getDeviceListRefreshSubject.and.returnValue(null);

      // Call & Verify (should not throw error)
      expect(() => component.onInitSubject()).not.toThrow();
      expect(component.subscriptionForRefreshList).toBeUndefined();
    });
  });

  describe('Private Methods Coverage', () => {
    describe('getpackageVersion', () => {
      it('should successfully get package versions and cache them', async () => {
        // Setup
        const mockPackageVersions = ['v1.0.0', 'v2.0.0'];
        const mockResponse = { body: mockPackageVersions } as any;
        deviceService.getpackageVersion.and.returnValue(of(mockResponse));
        commonsService.checkForNull.and.returnValue(mockPackageVersions);

        // Call
        await component['getpackageVersion']();

        // Verify
        expect(deviceService.getpackageVersion).toHaveBeenCalled();
        expect(commonsService.checkForNull).toHaveBeenCalledWith(mockResponse.body);
        expect(component.packageVersionsList).toEqual(mockPackageVersions);
        expect(deviceFilterService.setPackageVersionList).toHaveBeenCalledWith(mockPackageVersions);
      });

      it('should handle error in getpackageVersion', async () => {
        // Setup
        const error = new Error('API Error');
        deviceService.getpackageVersion.and.returnValue(throwError(() => error));

        // Call
        await component['getpackageVersion']();

        // Verify
        expect(exceptionService.customErrorMessage).toHaveBeenCalledWith(jasmine.any(Error));
      });
    });

    describe('updateSalesOrderList', () => {
      it('should successfully get sales order list and cache it', async () => {
        // Setup
        const mockSalesOrders = ['SO001', 'SO002'];
        salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(mockSalesOrders));

        // Call
        await component['updateSalesOrderList']();

        // Verify
        expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
        expect(component.salesOrderNumberList).toEqual(mockSalesOrders);
        expect(deviceFilterService.setSalesOrderNumberList).toHaveBeenCalledWith(mockSalesOrders);
      });
    });

    describe('getCountryList', () => {
      it('should successfully get country list and cache it', async () => {
        // Setup
        const mockCountries: CountryListResponse[] = [
          { id: 1, country: 'USA', languages: ['English'] }
        ];
        countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve(mockCountries));

        // Call
        await component['getCountryList']();

        // Verify
        expect(countryCacheService.getCountryListFromCache).toHaveBeenCalledWith(true);
        expect(component.countryList).toEqual(mockCountries);
        expect(deviceFilterService.setCountryList).toHaveBeenCalledWith(mockCountries);
      });
    });

    describe('initializeFilterForm', () => {
      it('should initialize form with all required controls and validators', () => {
        // Call
        component['initializeFilterForm']();

        // Verify all form controls exist
        expect(component.filterForm.get('packageVersions')).toBeDefined();
        expect(component.filterForm.get('connectionState')).toBeDefined();
        expect(component.filterForm.get('deviceLockState')).toBeDefined();
        expect(component.filterForm.get('deviceEditState')).toBeDefined();
        expect(component.filterForm.get('countries')).toBeDefined();
        expect(component.filterForm.get('drpDeviceType')).toBeDefined();
        expect(component.filterForm.get('salesOrderNumber')).toBeDefined();
        expect(component.filterForm.get('productStatus')).toBeDefined();
        expect(component.filterForm.get('deviceId')).toBeDefined();
        expect(component.filterForm.get('deviceSerialNo')).toBeDefined();
        expect(component.filterForm.get('customerName')).toBeDefined();

        // Verify validators are applied
        const deviceIdControl = component.filterForm.get('deviceId');
        const deviceSerialNoControl = component.filterForm.get('deviceSerialNo');
        const customerNameControl = component.filterForm.get('customerName');

        expect(deviceIdControl?.hasError('maxlength')).toBeFalsy();
        expect(deviceSerialNoControl?.hasError('maxlength')).toBeFalsy();
        expect(customerNameControl?.hasError('maxlength')).toBeFalsy();
      });
    });
  });

  describe('Edge Cases and Additional Coverage', () => {
    it('should handle null subscription in ngOnDestroy', () => {
      // Setup
      component.subscriptionForRefreshList = undefined;

      // Call & Verify (no error)
      expect(() => component.ngOnDestroy()).not.toThrow();
    });

    it('should handle undefined subscription in ngOnDestroy', () => {
      // Setup
      component.subscriptionForRefreshList = null;

      // Call & Verify (no error)
      expect(() => component.ngOnDestroy()).not.toThrow();
    });

    it('should initialize with correct default values', () => {
      // Verify
      expect(component.textBoxMaxCharactersAllowedMessage).toBeDefined();
      expect(component.small_textBoxMaxCharactersAllowedMessage).toBeDefined();
      expect(component.specialCharacterErrorMessage).toBeDefined();
      expect(component.defaultListingPageReloadSubjectParameter).toBeDefined();
    });

    it('should handle dropdown settings with specific parameters', () => {
      // Setup
      const mockSettings = { singleSelection: false, text: 'Select' };
      multiSelectService.getSalesOrderNumberDrpSetting.and.returnValue(mockSettings as any);
      multiSelectService.getCountryDrpSetting.and.returnValue(mockSettings as any);

      // Call
      component['initializeDropdownSettings']();

      // Verify specific method calls with parameters
      expect(multiSelectService.getSalesOrderNumberDrpSetting).toHaveBeenCalledWith(false, 'Select All', 'UnSelect All', false);
      expect(multiSelectService.getCountryDrpSetting).toHaveBeenCalledWith(false, false);
    });

    it('should handle country selection with id exactly -1', () => {
      // Setup
      const specialItem = { id: -1, country: 'Other' };
      const mockCountryList = [
        { id: 1, country: 'USA', languages: ['English'] }
      ];
      component.countryList = mockCountryList;
      multiSelectService.setOtherOptionDisabled.and.returnValue(mockCountryList);

      // Call
      component.onCountrySelect(specialItem);

      // Verify
      expect(component.filterForm.get('countries')?.value).toEqual([specialItem]);
      expect(multiSelectService.setOtherOptionDisabled).toHaveBeenCalledWith(mockCountryList);
    });

    it('should handle country selection with id not equal to -1', () => {
      // Setup
      const regularItem = { id: 0, country: 'Test' };
      spyOn(component.filterForm.get('countries'), 'setValue');

      // Call
      component.onCountrySelect(regularItem);

      // Verify special handling is not triggered
      expect(component.filterForm.get('countries')?.setValue).not.toHaveBeenCalled();
      expect(multiSelectService.setOtherOptionDisabled).not.toHaveBeenCalled();
    });

    it('should handle constructor initialization', () => {
      // Verify constructor calls initializeFilterForm
      expect(component.filterForm).toBeDefined();
    });

    it('should handle getInitCall with all cached data available', async () => {
      // Setup - all caches have data
      deviceFilterService.getPackageVersionList.and.returnValue(['v1.0.0']);
      deviceFilterService.getSalesOrderNumberList.and.returnValue(['SO001']);
      deviceFilterService.getCountryList.and.returnValue([{ id: 1, country: 'USA', languages: ['English'] }]);
      spyOn(component as any, 'setFilterValue');

      // Call
      await component.getInitCall();

      // Verify cached data is used and API calls are not made
      expect(component.packageVersionsList).toEqual(['v1.0.0']);
      expect(component.salesOrderNumberList).toEqual(['SO001']);
      expect(component.countryList).toEqual([{ id: 1, country: 'USA', languages: ['English'] }]);
      expect(component['setFilterValue']).toHaveBeenCalled();

      // Verify API methods are not called
      expect(deviceService.getpackageVersion).not.toHaveBeenCalled();
      expect(salesOrderApiCallService.getSalesOrderNumberList).not.toHaveBeenCalled();
      expect(countryCacheService.getCountryListFromCache).not.toHaveBeenCalled();
    });

    it('should handle getInitCall with empty caches', async () => {
      // Setup - all caches are empty
      deviceFilterService.getPackageVersionList.and.returnValue([]);
      deviceFilterService.getSalesOrderNumberList.and.returnValue([]);
      deviceFilterService.getCountryList.and.returnValue([]);

      // Setup API responses
      const mockResponse = { body: ['v1.0.0'] } as any;
      deviceService.getpackageVersion.and.returnValue(of(mockResponse));
      commonsService.checkForNull.and.returnValue(['v1.0.0']);
      salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));

      spyOn(component as any, 'setFilterValue');

      // Call
      await component.getInitCall();

      // Verify API calls are made
      expect(deviceService.getpackageVersion).toHaveBeenCalled();
      expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
      expect(countryCacheService.getCountryListFromCache).toHaveBeenCalled();
      expect(component['setFilterValue']).toHaveBeenCalled();
    });

    it('should handle form validation edge cases', () => {
      // Test empty string validation
      const deviceIdControl = component.filterForm.get('deviceId');
      const deviceSerialNoControl = component.filterForm.get('deviceSerialNo');
      const customerNameControl = component.filterForm.get('customerName');

      // Test empty values
      deviceIdControl?.setValue('');
      deviceSerialNoControl?.setValue('');
      customerNameControl?.setValue('');

      expect(deviceIdControl?.valid).toBeTruthy();
      expect(deviceSerialNoControl?.valid).toBeTruthy();
      expect(customerNameControl?.valid).toBeTruthy();

      // Test whitespace values
      deviceIdControl?.setValue('   ');
      deviceSerialNoControl?.setValue('   ');
      customerNameControl?.setValue('   ');

      expect(deviceIdControl?.valid).toBeTruthy();
      expect(deviceSerialNoControl?.valid).toBeTruthy();
      expect(customerNameControl?.valid).toBeTruthy();
    });

    it('should handle complex setFilterValue scenarios', () => {
      // Setup complex search request
      const mockSearchRequest = {
        packageVersions: ['v1.0.0'],
        status: 'CONNECTED',
        deviceLockStatus: false,
        isEditable: true,
        countryIds: [1],
        productStatus: [ProductStatusEnum.DISABLED],
        deviceType: 'TEST',
        salesOrderNumbers: ['SO001'],
        deviceId: '',
        deviceSerialNo: '',
        customerName: ''
      };
      component.deviceSearchRequestBody = mockSearchRequest;
      component.countryList = [{ id: 1, country: 'USA', languages: ['English'] }];
      component.lockUnlockStateList = [{ key: 'Unlocked', value: false }];
      component.editStateList = [{ key: 'Enabled', value: true }];

      commonsService.getEnumMappingSelectedValue.and.returnValue([{ key: 'CONNECTED', value: 'CONNECTED' }]);

      // Call
      component['setFilterValue']();

      // Verify all form values are set correctly
      expect(component.filterForm.get('packageVersions')?.value).toEqual(['v1.0.0']);
      expect(component.filterForm.get('salesOrderNumber')?.value).toEqual(['SO001']);
      expect(component.filterForm.get('deviceId')?.value).toBe('');
      expect(component.filterForm.get('deviceSerialNo')?.value).toBe('');
      expect(component.filterForm.get('customerName')?.value).toBe('');
    });

    it('should handle Input properties correctly', () => {
      // Test Input properties
      component.isFilterComponentInitWithApicall = true;
      component.listPageRefreshForbackToDetailPage = true;
      component.deviceSearchRequestBody = {
        packageVersions: ['v1.0.0'],
        status: 'CONNECTED',
        deviceLockStatus: true,
        isEditable: false,
        countryIds: [1],
        productStatus: [ProductStatusEnum.ENABLED],
        deviceType: 'CLIENT',
        salesOrderNumbers: ['SO001'],
        deviceId: 'DEV123',
        deviceSerialNo: 'SN123',
        customerName: 'Test Customer'
      };

      // Verify properties are set
      expect(component.isFilterComponentInitWithApicall).toBe(true);
      expect(component.listPageRefreshForbackToDetailPage).toBe(true);
      expect(component.deviceSearchRequestBody).toBeDefined();
    });

    it('should handle all dropdown data initialization', () => {
      // Call initialization
      component['initializeDropdownData']();

      // Verify all dropdown data is initialized
      expect(component.deviceTypes).toEqual([CLIENT_DEVICE, TEST_DEVICE, DEMO_DEVICE]);
      // Verify service calls
      expect(keyValueService.enumOptionToList).toHaveBeenCalledTimes(2);
      expect(keyValueService.lockedUnlockOptionList).toHaveBeenCalled();
      expect(keyValueService.editEnableDisableOptionList).toHaveBeenCalled();
    });

    it('should handle complete ngOnInit flow', () => {
      // Setup
      component.isFilterComponentInitWithApicall = true;
      spyOn(component, 'clearFilter');
      spyOn(component as any, 'initializeDropdownSettings');
      spyOn(component as any, 'initializeDropdownData');
      spyOn(component, 'getInitCall').and.returnValue(Promise.resolve());
      spyOn(component, 'onInitSubject');

      // Call
      component.ngOnInit();

      // Verify all initialization methods are called
      expect(component['initializeDropdownSettings']).toHaveBeenCalled();
      expect(component['initializeDropdownData']).toHaveBeenCalled();
      expect(component.getInitCall).toHaveBeenCalled();
      expect(component.onInitSubject).toHaveBeenCalled();
      expect(component.clearFilter).toHaveBeenCalledWith(component.defaultListingPageReloadSubjectParameter);
    });

    it('should handle subscription cleanup edge cases', () => {
      // Test with valid subscription
      const mockSubscription = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      component.subscriptionForRefreshList = mockSubscription;

      component.ngOnDestroy();

      expect(mockSubscription.unsubscribe).toHaveBeenCalled();

      // Test with falsy subscription
      component.subscriptionForRefreshList = null;
      expect(() => component.ngOnDestroy()).not.toThrow();

      component.subscriptionForRefreshList = undefined;
      expect(() => component.ngOnDestroy()).not.toThrow();
    });
  });

});
