import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DatePipe } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { UpdateProbeTypeComponent } from './update-probe-type.component';


describe('UpdateProbeTypeComponent', () => {
  let component: UpdateProbeTypeComponent;
  let fixture: ComponentFixture<UpdateProbeTypeComponent>;

  beforeEach(async () => {
    const toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    await TestBed.configureTestingModule({
      declarations: [UpdateProbeTypeComponent],
      imports: [ReactiveFormsModule],
      providers: [NgbActiveModal,
        ExceptionHandlingService,
        AuthJwtService,
        ProbeApiService,
        SessionStorageService,
        DatePipe,
        HidePermissionNamePipe,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(UpdateProbeTypeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
