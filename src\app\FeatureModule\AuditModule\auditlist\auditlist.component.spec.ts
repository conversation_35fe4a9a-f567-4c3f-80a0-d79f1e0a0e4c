import { Location } from '@angular/common';
import { HttpResponse } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { By } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NgbPagination } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { commonsProviders, testAuthentication, testDropdownInteraction, testPagination, testToggleFilter } from 'src/app/Tesing-Helper/test-utils';
import { INTERNAL_SERVER_ERROR, ITEMS_PER_PAGE } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { AuditListResponse } from 'src/app/model/Audit/AuditListResponse';
import { AuditModuleWithActionResponse } from 'src/app/model/Audit/AuditModuleWithActionResponse.model';
import { AuditListPageResponse } from 'src/app/model/Audit/auditListPageResponse';
import { BaseResponse } from 'src/app/model/common/BaseResponse.model';
import { Pageable } from 'src/app/model/common/pageable.model';
import { Sort } from 'src/app/model/sort.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { AuditApiCallService } from 'src/app/shared/Service/Audit/audit-api-call.service';
import { AuditService } from 'src/app/shared/Service/Audit/audit.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { ModuleDetailPageDisplayPermissionCheckPipe } from 'src/app/shared/pipes/ModuleDetailPageDisplayPermissionCheckPipe.pipe';
import { GetPermissionModuleName } from 'src/app/shared/pipes/Role/getPermissionModuleName.pipe';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { RoleDetailComponent } from '../../RoleModule/role-detail/role-detail.component';
import { AuditDetailModelComponent } from '../audit-detail-model/audit-detail-model.component';
import { AuditFilterComponent } from '../audit-filter/audit-filter.component';
import { AuditlistComponent } from './auditlist.component';
import { AuditDetailResponse } from 'src/app/model/Audit/auditDetailResponse.model';
import { AuditDataTypeEnum } from 'src/app/shared/enum/Audit/AuditDataTypeEnum.enum';

describe('AuditlistComponent', () => {
  let component: AuditlistComponent;
  let fixture: ComponentFixture<AuditlistComponent>;
  let authServiceMock: jasmine.SpyObj<AuthJwtService>;
  let permissionsServiceMock: jasmine.SpyObj<PermissionService>;
  let auditApiCallServiceMock: jasmine.SpyObj<AuditApiCallService>;
  let auditServiceMock: AuditService;
  let exceptionHandlingService: ExceptionHandlingService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let location: Location;

  const convertActions = (actions: Array<BaseResponse>) =>
    actions.map((action) => new BaseResponse(action.id, action.name, action.displayName));


  const sort = new Sort(true, false, false); // Unsigned, not sorted, not empty
  // Create the Pageable object
  const pageable = new Pageable(sort, 0, 10, 0, true, false);

  const MODULE_ACTIONS = {
    ROLE: [
      { name: "ROLE_CREATE", displayName: "Create Role" },
      { name: "ROLE_UPDATE", displayName: "Update Role" },
      { name: "ROLE_DELETE", displayName: "Delete Role" },
    ],
    USER: [
      { name: "USER_CHANGE_PASSWORD", displayName: "Change Password" },
      { name: "USER_LOGOUT", displayName: "Logout User" },
      { name: "USER_RESET_PASSWORD", displayName: "Reset Password" },
      { name: "USER_LOGIN", displayName: "Login User" },
      { name: "USER_SET_PASSWORD", displayName: "Set Password" },
      { name: "USER_CREATE", displayName: "Create User" },
      { name: "USER_UPDATE", displayName: "Update User" },
      { name: "USER_DELETE", displayName: "Delete User" },
    ],
    VIDEO: [
      { name: "VIDEO_DELETE_JSON", displayName: "Delete JSON" },
      { name: "VIDEO_ADD_VIDEO", displayName: "Add Video" },
      { name: "VIDEO_ADD_JSON", displayName: "Add JSON" },
      { name: "VIDEO_DELETE_VIDEO", displayName: "Delete Video" },
      { name: "VIDEO_UPDATE_JSON", displayName: "Update JSON" },
      { name: "VIDEO_UPDATE_VIDEO", displayName: "Update Video" },
    ],
    PROBE: [
      { name: "PROBE_LOCK_UNLOCK", displayName: "Lock / Unlock Probe" },
      { name: "PROBE_ASSIGN_FEATURE", displayName: "Configure License" },
      { name: "PROBE_ADD", displayName: "Add Probe" },
      { name: "PROBE_UPDATE_INFO", displayName: "Add/Update Probe" },
      { name: "PROBE_DELETE", displayName: "Delete Probe" },
      { name: "PROBE_ENABLE_DISABLE_EDIT", displayName: "Allowed / Not Allowed Edit Probe" },
      { name: "PROBE_UPDATE_TYPE", displayName: "Update Probe Type" },
      { name: "PROBE_UPDATE_IMPORT_CSV", displayName: "Import CSV - Update Probe" },
      { name: "PROBE_CUSTOMER_SALESORDER_ASSOCIATION", displayName: "Customer / Sales Order Association" },
      { name: "PROBE_DISABLE", displayName: "Disable Probe" },
      { name: "PROBE_RMA", displayName: "RMA Probe" },
    ],
    SOFTWARE_BUILD: [
      { name: "SOFTWARE_BUILD_ACTIVE_INACTIVE", displayName: "Active / Inactive Software Build" },
      { name: "SOFTWARE_BUILD_UPDATE", displayName: "Update Software Build" },
      { name: "SOFTWARE_BUILD_DELETE", displayName: "Delete Software Build" },
      { name: "SOFTWARE_BUILD_SAVE", displayName: "Upload Software Build" },
      { name: "SOFTWARE_BUILD_UPDATE_TYPE", displayName: "Associate Device Type" },
    ],
    PROBE_CONFIG_GROUP: [
      { name: "PROBE_CONFIG_GROUP_DELETE", displayName: "Delete Probe Config Group" },
      { name: "PROBE_CONFIG_GROUP_ADD", displayName: "Add Probe Config Group" },
    ],
    SALES_ORDER: [
      { name: "SALES_ORDER_SCHEDULER_START", displayName: "Schedular Start" },
      { name: "SALES_ORDER_PROBE_DISABLE", displayName: "Disable Probe" },
      { name: "SALES_ORDER_DEVICE_DISABLE", displayName: "Disable Device" },
      { name: "SALES_ORDER_SCHEDULER_COMPLETE", displayName: "Schedular Stop" },
      { name: "SALES_ORDER_BRIDGE_CONFIGURE", displayName: "Device Configuration - Completed" },
      { name: "SALES_ORDER_BRIDGE_FAILED", displayName: "Device Configuration - Error" },
      { name: "SALES_ORDER_UPDATE_USER_DEFINED_ORDER", displayName: "Update User Defined Order" },
      { name: "SALES_ORDER_SYNC_SUCCESS", displayName: "Sync Success" },
      { name: "SALES_ORDER_PROBE_CONFIGURE", displayName: "Configure Probe" },
      { name: "SALES_ORDER_BRIDGE_IN_PROGRESS", displayName: "Device Configuration - Started" },
      { name: "SALES_ORDER_PROBE_RMA", displayName: "RMA Probe" },
      { name: "SALES_ORDER_ADD_USER_DEFINED_ORDER", displayName: "Add User Defined Order" },
      { name: "SALES_ORDER_SYNC_ERROR", displayName: "Sync Error" },
      { name: "SALES_ORDER_DEVICE_RMA", displayName: "RMA Device" },
      { name: "SALES_ORDER_BRIDGE_RESET", displayName: "Reset Device" },
      { name: "SALES_ORDER_PROBE_DELETE", displayName: "Delete Probe" },
    ],
    DEVICE: [
      { name: "DEVICE_UPDATE_IMPORT_CSV", displayName: "Import CSV - Update Device" },
      { name: "DEVICE_DISABLE", displayName: "Disable Device" },
      { name: "SAS_URI_GENERATED", displayName: "SAS URI Generated to Upload Us2 Logs" },
      { name: "DEVICE_UPDATE_TYPE", displayName: "Update Device Type" },
      { name: "DEVICE_LOCK_UNLOCK", displayName: "Lock / Unlock Device" },
      { name: "DEVICE_CUSTOMER_SALESORDER_ASSOCIATION", displayName: "Customer / Sales Order Association" },
      { name: "DEVICE_ASSIGN_RELEASE_VERSION", displayName: "Release Version Association to Test Device" },
      { name: "DEVICE_REMOVE_COUNTRY", displayName: "Remove Country Association" },
      { name: "DEVICE_RMA", displayName: "RMA Device" },
      { name: "DEVICE_REGISTER_UPDATE", displayName: "Update Device Registration" },
      { name: "DEVICE_COUNTRY_ASSOCIATION", displayName: "Country Association" },
      { name: "DEVICE_UPDATE_REQUEST", displayName: "Device Update Requested" },
      { name: "DEVICE_CONNECTED", displayName: "Device Connected" },
      { name: "DEVICE_GENERATE_CERTIFICATE", displayName: "Certificate Generated" },
      { name: "DEVICE_REGISTER_ADD", displayName: "Device Registered" },
      { name: "DEVICE_ENABLE_DISABLE_EDIT", displayName: "Allowed / Not Allowed Edit Device" },
      { name: "DEVICE_UPDATE_INFO", displayName: "Programmer - Device Update" },
      { name: "DEVICE_DISCONNECTED", displayName: "Device Disconnected" },
    ],
    KIT_MANAGEMENT: [
      { name: "KIT_MANGEMENT_DELETE_IMPORT_CSV", displayName: "Import CSV - Delete Bridge Kit" },
      { name: "OTS_KIT_MANGEMENT_ADD_IMPORT_CSV", displayName: "Import CSV - Add OTS Kit" },
      { name: "OTS_KIT_MANGEMENT_DELETE_IMPORT_CSV", displayName: "Import CSV - Delete OTS Kit" },
      { name: "KIT_MANGEMENT_ADD_IMPORT_CSV", displayName: "Import CSV - Add Bridge Kit" },
    ],
  };

  const createAuditModule = (name: string, displayName: string, actions: Array<{ name: string, displayName: string }>) => {
    return new AuditModuleWithActionResponse(
      null,
      name,
      displayName,
      convertActions(actions.map(action => ({
        id: null,
        name: action.name,
        displayName: action.displayName
      })))
    );
  };

  const getAuditModuleListResponse = [
    createAuditModule("ROLE", "ROLE", MODULE_ACTIONS.ROLE),
    createAuditModule("USER", "USERS", MODULE_ACTIONS.USER),
    createAuditModule("VIDEO", "VIDEOS", MODULE_ACTIONS.VIDEO),
    createAuditModule("PROBE", "PROBES", MODULE_ACTIONS.PROBE),
    createAuditModule("SOFTWARE_BUILD", "SOFTWARE BUILDS", MODULE_ACTIONS.SOFTWARE_BUILD),
    createAuditModule("PROBE_CONFIG_GROUP", "PROBE CONFIG GROUP", MODULE_ACTIONS.PROBE_CONFIG_GROUP),
    createAuditModule("SALES_ORDER", "SALES ORDER", MODULE_ACTIONS.SALES_ORDER),
    createAuditModule("DEVICE", "DEVICES", MODULE_ACTIONS.DEVICE),
    createAuditModule("KIT_MANAGEMENT", "KIT MANAGEMENT", MODULE_ACTIONS.KIT_MANAGEMENT),
  ];

  const content: Array<AuditListResponse> = [
    new AuditListResponse(32403, "ROLE", 72, "Device / SV Team", "Create Role", "anil.agrawal", 1721367934542, true, true, false, true),
    new AuditListResponse(58958, "PROBES", 3623, "T1B-20241106-1", "Add/Update Probe", "mohit.kanani", 1730870528555, true, true, false, true),
    new AuditListResponse(58957, "SALES ORDER", -1, "Auto Sync", "Schedular Stop", "system", 1730870460715, false, false, false, false),
    new AuditListResponse(58956, "SALES ORDER", -1, "Auto Sync", "Schedular Start", "system", 1730870460000, false, false, false, false),
    new AuditListResponse(58955, "USERS", 158, "mohit.kanani", "Login User", "mohit.kanani", 1730870455267, false, false, false, false),
    new AuditListResponse(58954, "SALES ORDER", -1, "Auto Sync", "Schedular Stop", "system", 1730870280624, false, false, false, true),
    new AuditListResponse(58953, "SALES ORDER", -1, "Auto Sync", "Schedular Start", "system", 1730870280000, false, false, false, true),
    new AuditListResponse(58952, "SALES ORDER", -1, "Auto Sync", "Schedular Stop", "system", 1730870100668, false, false, false, false),
    new AuditListResponse(58951, "SALES ORDER", -1, "Auto Sync", "Schedular Start", "system", 1730870100000, false, false, false, false),
    new AuditListResponse(58950, "SALES ORDER", -1, "Auto Sync", "Schedular Stop", "system", 1730869920553, false, false, false, true),
  ];

  // Create AuditListPageResponse instance
  const auditListPageResponse = new AuditListPageResponse(pageable, 5, false, 50, 10, true, sort, 10, 0, false, content);

  const mockData: AuditDetailResponse[] = [
    new AuditDetailResponse("Sales Order Number", null, null, AuditDataTypeEnum.TYPE_STRING, "salesOrderNumber", false),
    new AuditDetailResponse("Customer Name", null, null, AuditDataTypeEnum.TYPE_STRING, "customerName", false),
    new AuditDetailResponse("Customer E-mail", null, null, AuditDataTypeEnum.TYPE_STRING, "customerEmail", false),
    new AuditDetailResponse("Locked / Unlocked", null, "false", AuditDataTypeEnum.TYPE_BOOLEAN, "locked", false),
    new AuditDetailResponse("Editable", null, "true", AuditDataTypeEnum.TYPE_BOOLEAN, "isEditable", false),
    new AuditDetailResponse("Country", null, null, AuditDataTypeEnum.TYPE_STRING, "country", false),
    new AuditDetailResponse("Type", null, "Lexsa", AuditDataTypeEnum.TYPE_STRING, "probeType", false),
    new AuditDetailResponse("Part Number", null, "P006539-002", AuditDataTypeEnum.TYPE_STRING, "partNumber", false),
    new AuditDetailResponse("Probe Version", null, "1.0.23", AuditDataTypeEnum.TYPE_STRING, "probeVersion", false),
    new AuditDetailResponse("Last Connected Date & Time", null, "1741079537931", AuditDataTypeEnum.TYPE_DATE, "lastConnectedTime", false),
    new AuditDetailResponse("Device Serial Number", null, "054F22AC-AF1C-4AFE-A2A5-EFA749734691", AuditDataTypeEnum.TYPE_STRING, "deviceSerialNumber", false),
    new AuditDetailResponse("Features", null, "PW Doppler", AuditDataTypeEnum.TYPE_STRING, "features", true),
    new AuditDetailResponse("Presets", null, "Nerve, Lungs Lexsa, Msk, Vascular", AuditDataTypeEnum.TYPE_DATE, "presets", true),
    new AuditDetailResponse("Status", null, "DISABLED", AuditDataTypeEnum.TYPE_ENUM, "productStatus", false)
  ];

  beforeEach(async () => {
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    authServiceMock = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    permissionsServiceMock = jasmine.createSpyObj('PermissionService', [
      'getDevicePermission',
      'getKitManagementPermission',
      'getProbPermission',
      'getUserPermission',
      'getRolePermission',
      'getSalesOrderPermission',
      'getProbeConfigGroupPermission',
    ]);
    auditApiCallServiceMock = jasmine.createSpyObj('auditApiCallService', ['getAuditModuleList', 'getAuditList', 'getAuditDetail']);
    exceptionHandlingService = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    // Provide mock implementations for methods
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    // Mock the API call response
    auditApiCallServiceMock.getAuditModuleList.and.returnValue(Promise.resolve(getAuditModuleListResponse));

    await TestBed.configureTestingModule({
      declarations: [AuditlistComponent, AuditFilterComponent, ModuleDetailPageDisplayPermissionCheckPipe, RoleDetailComponent, PrintListPipe, GetPermissionModuleName, AuditDetailModelComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      imports: [NgbPagination, FormsModule, ReactiveFormsModule, NgMultiSelectDropDownModule.forRoot(), MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatDatepickerModule,
        MatNativeDateModule,
        BrowserAnimationsModule],
      providers: [
        { provide: AuthJwtService, useValue: authServiceMock },
        CommonsService,
        SessionStorageService,
        HidePermissionNamePipe,
        PrintListPipe,
        AuditService,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: PermissionService, useValue: permissionsServiceMock },
        { provide: AuditApiCallService, useValue: auditApiCallServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(AuditlistComponent);
    auditServiceMock = TestBed.inject(AuditService);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    location = TestBed.inject(Location);
    component = fixture.componentInstance;
    await fixture.whenStable();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form controls on ngOnInit', async () => {
    // Arrange
    auditApiCallServiceMock.getAuditList.and.returnValue(of(new HttpResponse<AuditListPageResponse>({
      body: auditListPageResponse,
      status: 200,
      statusText: 'OK',
    })));
    authServiceMock.isAuthenticate.and.returnValue(true);
    permissionsServiceMock.getKitManagementPermission.and.returnValue(true);

    spyOn(auditServiceMock, 'getModulePermissionMapping').and.returnValue(new Map<string, boolean>([
      ['DEVICES', true],
      ['PROBES', true],
      ['USERS', true],
      ['ROLE', true],
      ['SALES ORDER', true],
      ['KIT MANAGEMENT', true],
      ['PROBE CONFIG GROUP', true]
    ]));
    spyOn(auditServiceMock, 'callAuditListFilterRequestParameterSubject').and.callThrough();
    spyOn(component, 'loadAll').and.callThrough();

    // Act
    fixture.detectChanges(); // Ensure initial change detection
    // Check if the component exists
    // Access the filter component's instance
    // Now we can access filterComponent properties and continue with further assertions
    component.ngOnInit();
    fixture.detectChanges(); // Ensure the view updates after ngOnInit

    const filterComponent = fixture.debugElement.query(By.directive(AuditFilterComponent)).componentInstance;
    expect(filterComponent).toBeTruthy();

    expect(filterComponent.filterAuditForm.get('actions').value).toEqual([]);
    expect(filterComponent.filterAuditForm.get('uniqueId').value).toBe(null);
    expect(filterComponent.filterAuditForm.get('user').value).toBe(null);
    expect(filterComponent.filterAuditForm.get('modifiedStartDate').value).toBe(null);
    expect(filterComponent.filterAuditForm.get('modifiedEndDate').value).toBe(null);

    // Additional Assertions...
    expect(component.otsKitManagementPermission).toBeTrue();
    expect(component.brigeKitManagementPermission).toBeTrue();
    expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE);
    expect(component.drpselectsize).toBe(ITEMS_PER_PAGE);
    expect(component.previousPage).toBe(1);
    expect(component.dataSizes).toEqual(['10', '25', '50', '100']);

    expect(auditServiceMock.callAuditListFilterRequestParameterSubject).toHaveBeenCalled();
    expect(auditServiceMock.getModulePermissionMapping).toHaveBeenCalled();
    expect(auditApiCallServiceMock.getAuditModuleList).toHaveBeenCalled();


    expect(component.totalItems).toEqual(auditListPageResponse.totalElements);
    expect(component.auditListResponse).toEqual(auditListPageResponse.content);
    expect(component.totalRecord).toEqual(auditListPageResponse.totalElements);
    expect(component.totalRecordDisplay).toEqual(auditListPageResponse.numberOfElements);
  });


  it('LoadAll method gives error', fakeAsync(() => {
    // Arrange
    authServiceMock.isAuthenticate.and.returnValue(true);
    auditApiCallServiceMock.getAuditList.and.returnValue(of(new HttpResponse<AuditListPageResponse>({
      body: null,
      status: 205,
      statusText: 'Server Error',
    })));

    // Act
    component.loadAll(null);
    tick();

    // Assert
    expect(component.auditListResponse).toEqual([]); // Expect an empty list on error
    expect(component.totalRecordDisplay).toEqual(0);
    expect(component.totalRecord).toEqual(0);
    expect(component.loading).toBeFalse();
  }));

  it('should call toastrService.error with INTERNAL_SERVER_ERROR', () => {
    auditApiCallServiceMock.getAuditList.and.returnValue(
      throwError(() => ({
        status: 500, // HTTP status code indicating a server error
        statusText: 'Internal Server Error',
        message: 'Server error'
      }))
    );

    // Spy on the customErrorMessage method in exceptionHandlingService to verify error handling
    spyOn(exceptionHandlingService, 'customErrorMessage').and.callThrough();

    // Act: Trigger the component's initialization to invoke getJobList and error handling
    component.loadAll(null);

    // Assert: Ensure customErrorMessage was called for error handling
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();

    // Assert: Confirm toastrService.error was called with an INTERNAL_SERVER_ERROR message
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
  });

  it('should toggle filter visibility and update the button text in Audit List', () => {
    // Use the generic utility to test the toggle behavior
    testToggleFilter(component);
  });

  it('should initialize the component when the user is authenticated', async () => {

    authServiceMock.isAuthenticate.and.returnValue(true);
    // Initialize component and trigger change detection
    component.ngOnInit();
    fixture.detectChanges();

    spyOn(component, 'refreshFilter')?.and.callThrough();
    const button = fixture.nativeElement.querySelector('#refresh_auditList');
    button?.click();
    expect(component.refreshFilter).toHaveBeenCalled();

    testAuthentication(authServiceMock, component, fixture);

    // Test dropdown interaction
    testDropdownInteraction(fixture, component, '#auditListShowEntry');

    // Test pagination
    testPagination(fixture, component, '#audit-pagination', 2);
  });

  it('should render app-country-filter with correct inputs', () => {
    authServiceMock.isAuthenticate.and.returnValue(true);
    // Initialize component and trigger change detection
    component.ngOnInit();
    fixture.detectChanges();

    // Access the filter component's instance
    const filterComponent = fixture.debugElement.query(By.directive(AuditFilterComponent)).componentInstance;
    expect(filterComponent).toBeTruthy();

    // Now access the filterCountryForm property on the component instance
    // Initialize form controls in filterAuditForm with the correct values
    filterComponent.filterAuditForm.get('modules').setValue([{ name: 'ROLE', displayName: 'ROLE' }]);
    filterComponent.filterAuditForm.get('actions').setValue([{ name: 'ROLE_CREATE', displayName: 'Create Role' }]); // Should be an array
    filterComponent.filterAuditForm.get('uniqueId').setValue('akshay.dobariya'); // String
    filterComponent.filterAuditForm.get('user').setValue('akshay.dobariya'); // String

    // Spy on the service method
    spyOn(auditServiceMock, 'callAuditListFilterRequestParameterSubject').and.callThrough();
    spyOn(component, 'loadAll').and.callThrough();

    // Simulate a click on the select element (make sure the ID matches)
    const selectElement = fixture.nativeElement.querySelector('#auditFilterSearch');
    selectElement.click();

    // Trigger change detection again after setting the values
    fixture.detectChanges();

    // Verify that the service method was called
    expect(auditServiceMock.callAuditListFilterRequestParameterSubject).toHaveBeenCalled();
    expect(component.loadAll).toHaveBeenCalledWith(jasmine.objectContaining({
      auditModule: 'ROLE',
      auditAction: ['ROLE_CREATE'],
      uniqueId: 'akshay.dobariya',
      modifiedBy: 'akshay.dobariya',
      startmodifiedDate: null,
      endModifiedDate: null
    }));
  });
  it("auditSearchRequsetBody is not null", async () => {
    authServiceMock.isAuthenticate.and.returnValue(true);
    // Ensure the mock returns a value
    spyOn(auditServiceMock, 'getModulePermissionMapping').and.returnValue(new Map<string, boolean>([
      ['DEVICES', true],
      ['PROBES', true],
      ['USERS', true],
      ['ROLE', true],
      ['SALES ORDER', true],
      ['KIT MANAGEMENT', true],
      ['PROBE CONFIG GROUP', true]
    ]));

    auditApiCallServiceMock.getAuditList.and.returnValue(of(new HttpResponse<AuditListPageResponse>({
      body: auditListPageResponse,
      status: 200,
      statusText: 'OK',
    })));

    // Initialize component and trigger change detection
    component.ngOnInit();
    await fixture.whenStable();
    fixture.detectChanges();

    // Access the filter component's instance
    const filterComponentDebugElement = fixture.debugElement.query(By.directive(AuditFilterComponent));
    expect(filterComponentDebugElement).toBeTruthy();
    const filterComponent = filterComponentDebugElement?.componentInstance;
    expect(filterComponent).toBeTruthy();

    // Set form control values in filterAuditForm
    filterComponent.filterAuditForm.get('modules').setValue([{ name: 'ROLE', displayName: 'ROLE' }]);
    filterComponent.filterAuditForm.get('actions').setValue([{ name: 'ROLE_CREATE', displayName: 'Create Role' }]);
    filterComponent.filterAuditForm.get('uniqueId').setValue('akshay.dobariya');
    filterComponent.filterAuditForm.get('user').setValue('akshay.dobariya');


    // Spy on the service method
    spyOn(auditServiceMock, 'callAuditListFilterRequestParameterSubject').and.callThrough();
    spyOn(component, 'loadAll').and.callThrough();

    // Simulate a click on the select element
    const selectElement = fixture.nativeElement.querySelector('#auditFilterSearch');
    selectElement?.click();
    await fixture.whenStable();
    fixture.detectChanges();

    // Call setFilterValue to apply filter
    filterComponent.setFilterValue();
    fixture.detectChanges();

    // Verify that the service method was called
    const selectElementDetails = fixture.nativeElement.querySelector('#auditToDeatil');
    selectElementDetails?.click();
    fixture.detectChanges();

    // Access the details component
    const detailsComponentDebugElement = fixture.debugElement.query(By.directive(RoleDetailComponent));
    expect(detailsComponentDebugElement).toBeTruthy();
    const detailsComponent = detailsComponentDebugElement?.componentInstance;
    expect(detailsComponent).toBeTruthy();

    // Interact with the details component and the filter component
    detailsComponent.back();
    filterComponent.ngOnInit();

    expect(auditServiceMock.callAuditListFilterRequestParameterSubject).toHaveBeenCalled();
    expect(component.loadAll).toHaveBeenCalledWith(jasmine.objectContaining({
      auditModule: 'ROLE',
      auditAction: ['ROLE_CREATE'],
      uniqueId: 'akshay.dobariya',
      modifiedBy: 'akshay.dobariya',
      startmodifiedDate: null,
      endModifiedDate: null
    }));
  });

  it("audit view details", async () => {
    authServiceMock.isAuthenticate.and.returnValue(true);
    spyOn(auditServiceMock, "openAuditDetailPopup").and.callThrough();
    auditApiCallServiceMock.getAuditList.and.returnValue(of(new HttpResponse<AuditListPageResponse>({
      body: auditListPageResponse,
      status: 200,
      statusText: 'OK',
    })));

    auditApiCallServiceMock.getAuditDetail.and.returnValue(of(new HttpResponse<Array<AuditDetailResponse>>({
      body: mockData,
      status: 200,
      statusText: 'OK',
    })));

    // Initialize component and trigger change detection
    component.ngOnInit();
    fixture.detectChanges();

    spyOn(component, 'loadAll').and.callThrough();

    // Simulate a click on the select element
    const selectElement = fixture.nativeElement.querySelector('#auditViewDetail');
    selectElement.click();
    fixture.detectChanges();

    // Wait for asynchronous tasks to complete
    await fixture.whenStable();

    expect(auditServiceMock.openAuditDetailPopup).toHaveBeenCalled();
  });
  it('when unauthenticat user try to access component', async () => {
    // `loginNavigate` is already spied on in the beforeEach setup, so no need to call `spyOn` here
    authServiceMock.isAuthenticate.and.returnValue(false);

    component.ngOnInit();

    // Use async check to wait for navigation to complete
    await fixture.whenStable();

    // Expect `loginNavigate` to have been called
    expect(authServiceMock.loginNavigate).toHaveBeenCalled();

    // Assert that the path has changed to the login page (empty path in this case)
    expect(location.path()).toBe('');
  });

});
