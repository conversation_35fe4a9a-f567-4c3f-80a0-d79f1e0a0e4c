import { TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { Subject, of, throwError } from 'rxjs';
import { DEVICE_ALREADY_CLIENT, DEVICE_ALREADY_DEMO, DEVICE_ALREADY_TEST, DEVICE_ALREADY_LOCKED, DEVICE_ALREADY_UNLOCKED, DEVICE_ALREADY_EDIT_ENABLE, DEVICE_ALREADY_EDIT_DISABLE, DEVICE_CONVERT_TO_CLIENT, DEVICE_CONVERT_TO_DEMO, DEVICE_CONVERT_TO_TEST, DeviceDetailResource, DeviceListResource, Device_Select_Message } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { AssignSelectedReleaseVersionRequest } from 'src/app/model/device/AssignSelectedReleaseVersionRequest.model';
import { DeviceFilterAction } from 'src/app/model/device/DeviceFilterAction.model';
import { DeviceSearchRequest } from 'src/app/model/device/deviceSearchRequest.model';
import { TransferProductDetails } from 'src/app/model/device/TransferProductDetails.model';

import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { DeviceOperationService } from './device-operation.service';
import { DeviceService } from 'src/app/shared/device.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { ModuleValidationServiceService } from 'src/app/shared/util/module-validation-service.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { deviceTypesEnum } from 'src/app/shared/enum/deviceTypesEnum.enum';
import { PermissionService } from 'src/app/shared/permission.service';
import { DeviceExportCSVSearchRequest } from 'src/app/model/device/DeviceExportCSVSearchRequest.model';
import { DeviceListOperations } from 'src/app/shared/enum/Operations/DeviceListOperations.enum';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';

describe('DeviceOperationService', () => {
  let service: DeviceOperationService;
  let deviceService: jasmine.SpyObj<DeviceService>;
  let salesOrderApiCallService: jasmine.SpyObj<SalesOrderApiCallService>;
  let countryCacheService: jasmine.SpyObj<CountryCacheService>;
  let commonsService: jasmine.SpyObj<CommonsService>;
  let exceptionHandlingService: jasmine.SpyObj<ExceptionHandlingService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let downloadService: jasmine.SpyObj<DownloadService>;
  let moduleValidationService: jasmine.SpyObj<ModuleValidationServiceService>;
  let permissionService: jasmine.SpyObj<PermissionService>;

  beforeEach(() => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const deviceServiceSpy = jasmine.createSpyObj('DeviceService', [
      'getpackageVersion', 'getDeviceList', 'updateDeviceTypeToTest', 'updateDeviceTypeToClient',
      'updateDeviceTypeToDemo', 'updateDeviceState', 'editEnableDisableForDevice',
      'disableProductStatusForDevice', 'rmaProductStatusForDevice', 'associationDeviceWithSalesOrder',
      'generateCSVFileForDevice', 'downloadCSVFileForDevice', 'getDeviceDetail',
      'getReleaseVersionDetail', 'assignSelectedReleaseVersion'
    ]);
    const salesOrderApiCallServiceSpy = jasmine.createSpyObj('SalesOrderApiCallService', ['getSalesOrderNumberList']);
    const countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache']);
    const commonsServiceSpy = jasmine.createSpyObj('CommonsService', [
      'checkForNull', 'checkValueIsNullOrEmpty', 'checkNullFieldValue', 'getIdsFromArray',
      'getSelectedValueFromEnum', 'getSelectedValueFromBooleanKeyValueMapping', 'getDeviceTypeStringToEnum'
    ]);
    const exceptionHandlingServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    const downloadServiceSpy = jasmine.createSpyObj('DownloadService', ['downloadExportCSV']);
    const moduleValidationServiceSpy = jasmine.createSpyObj('ModuleValidationServiceService', [
      'validateWithEditableWithMultipalRecoard', 'validateWithUserCountryForMultileRecord',
      'validateWithEditStateForSingleRecord', 'validateWithUserCountryForSingleRecord'
    ]);
    const permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getDevicePermission']);

    TestBed.configureTestingModule({
      providers: [
        { provide: DeviceService, useValue: deviceServiceSpy },
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: ExceptionHandlingService, useValue: exceptionHandlingServiceSpy },
        { provide: DownloadService, useValue: downloadServiceSpy },
        { provide: ModuleValidationServiceService, useValue: moduleValidationServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        AuthJwtService,
        LocalStorageService,
        SessionStorageService,
        commonsProviders(toastrServiceMock)
      ]
    });

    service = TestBed.inject(DeviceOperationService);
    deviceService = TestBed.inject(DeviceService) as jasmine.SpyObj<DeviceService>;
    salesOrderApiCallService = TestBed.inject(SalesOrderApiCallService) as jasmine.SpyObj<SalesOrderApiCallService>;
    countryCacheService = TestBed.inject(CountryCacheService) as jasmine.SpyObj<CountryCacheService>;
    commonsService = TestBed.inject(CommonsService) as jasmine.SpyObj<CommonsService>;
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService) as jasmine.SpyObj<ExceptionHandlingService>;
    downloadService = TestBed.inject(DownloadService) as jasmine.SpyObj<DownloadService>;
    moduleValidationService = TestBed.inject(ModuleValidationServiceService) as jasmine.SpyObj<ModuleValidationServiceService>;
    permissionService = TestBed.inject(PermissionService) as jasmine.SpyObj<PermissionService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  // ==================== BASIC FUNCTIONALITY TESTS ====================

  it('should get device list filter request parameter subject', () => {
    expect(service.getDeviceListFilterRequestParameterSubject()).toBeInstanceOf(Subject);
  });

  it('should get device list refresh subject', () => {
    expect(service.getDeviceListRefreshSubject()).toBeInstanceOf(Subject);
  });

  it('should set and get package version list', () => {
    const packageVersions = ['v1.0.0', 'v1.1.0', 'v2.0.0'];
    service.setPackageVersionList(packageVersions);
    expect(service.getPackageVersionList()).toEqual(packageVersions);
  });

  it('should set and get sales order number list', () => {
    const salesOrderNumbers = ['SO001', 'SO002', 'SO003'];
    service.setSalesOrderNumberList(salesOrderNumbers);
    expect(service.getSalesOrderNumberList()).toEqual(salesOrderNumbers);
  });

  it('should set and get country list', () => {
    const countries: CountryListResponse[] = [
      { id: 1, country: 'USA', languages: ['English'] },
      { id: 2, country: 'Canada', languages: ['English', 'French'] }
    ];
    service.setCountryList(countries);
    expect(service.getCountryList()).toEqual(countries);
  });

  it('should call device list filter request parameter subject', () => {
    const deviceSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    const deviceFilterAction = new DeviceFilterAction(listingPageReloadSubjectParameter, deviceSearchRequest);

    spyOn(service.getDeviceListFilterRequestParameterSubject(), 'next');

    service.callDeviceListFilterRequestParameterSubject(deviceFilterAction);

    expect(service.getDeviceListFilterRequestParameterSubject().next).toHaveBeenCalledWith(deviceFilterAction);
  });

  it('should call device list refresh subject', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

    spyOn(service.getDeviceListRefreshSubject(), 'next');

    service.callDeviceListRefreshSubject(listingPageReloadSubjectParameter);

    expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalledWith(listingPageReloadSubjectParameter);
  });

  // ==================== CACHE MANAGEMENT TESTS ====================

  it('should update cache in background with fresh API data', async () => {
    const apiPackageVersions = ['v3.0.0', 'v4.0.0'];
    const apiSalesOrders = ['SO004', 'SO005'];
    const apiCountries: CountryListResponse[] = [
      { id: 3, country: 'Germany', languages: ['German'] }
    ];

    deviceService.getpackageVersion.and.returnValue(of({ body: apiPackageVersions } as any));
    salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(apiSalesOrders));
    countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve(apiCountries));
    commonsService.checkForNull.and.returnValue(apiPackageVersions);

    await service.updateCacheInBackground();

    expect(service.getPackageVersionList()).toEqual(apiPackageVersions);
    expect(service.getSalesOrderNumberList()).toEqual(apiSalesOrders);
    expect(service.getCountryList()).toEqual(apiCountries);
    expect(deviceService.getpackageVersion).toHaveBeenCalled();
    expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
    expect(countryCacheService.getCountryListFromCache).toHaveBeenCalledWith(true);
  });

  it('should update only sales order cache when updateSalesOrderCacheOnly is called', async () => {
    service.setPackageVersionList(['v1.0.0']);
    service.setSalesOrderNumberList(['SO001']);
    service.setCountryList([{ id: 1, country: 'USA', languages: ['English'] }]);

    const newSalesOrders = ['SO001', 'SO002', 'SO003'];
    salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(newSalesOrders));

    await service.updateSalesOrderCacheOnly();

    expect(service.getSalesOrderNumberList()).toEqual(newSalesOrders);
    expect(service.getPackageVersionList()).toEqual(['v1.0.0']);
    expect(service.getCountryList()).toEqual([{ id: 1, country: 'USA', languages: ['English'] }]);
    expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
    expect(deviceService.getpackageVersion).not.toHaveBeenCalled();
    expect(countryCacheService.getCountryListFromCache).not.toHaveBeenCalled();
  });

  it('should handle null response from package version API', async () => {
    const mockResponse = { body: null };
    deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
    salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
    countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));
    commonsService.checkForNull.and.returnValue(null);

    await service.updateCacheInBackground();

    expect(commonsService.checkForNull).toHaveBeenCalledWith(null);
    expect(service.getPackageVersionList()).toEqual([]);
  });

  // ==================== REFRESH PAGE SUBJECT TESTS ====================

  it('should call refresh page subject for DeviceListResource with filter hidden', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    service.callRefreshPageSubject(
      listingPageReloadSubjectParameter,
      DeviceListResource,
      true,
      deviceSearchRequest
    );

    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
      jasmine.any(DeviceFilterAction)
    );
  });

  it('should call refresh page subject for DeviceListResource with filter not hidden', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

    spyOn(service.getDeviceListRefreshSubject(), 'next');

    service.callRefreshPageSubject(
      listingPageReloadSubjectParameter,
      DeviceListResource,
      false,
      deviceSearchRequest
    );

    expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalledWith(listingPageReloadSubjectParameter);
  });

  it('should call refresh page subject with clear filter', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
    const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    service.callRefreshPageSubject(
      listingPageReloadSubjectParameter,
      DeviceListResource,
      true,
      deviceSearchRequest
    );

    const expectedDeviceFilterAction = jasmine.objectContaining({
      deviceSearchRequest: jasmine.objectContaining({
        packageVersions: null
      })
    });

    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(expectedDeviceFilterAction);
  });

  it('should call refresh page subject with null device search request', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    service.callRefreshPageSubject(
      listingPageReloadSubjectParameter,
      DeviceListResource,
      true,
      null
    );

    const expectedDeviceFilterAction = jasmine.objectContaining({
      deviceSearchRequest: jasmine.objectContaining({
        packageVersions: null
      })
    });

    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(expectedDeviceFilterAction);
  });

  it('should not call any subject when resource is not DeviceListResource', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

    spyOn(service, 'callDeviceListFilterRequestParameterSubject');
    spyOn(service.getDeviceListRefreshSubject(), 'next');

    service.callRefreshPageSubject(
      listingPageReloadSubjectParameter,
      'OTHER_RESOURCE',
      true,
      deviceSearchRequest
    );

    expect(service.callDeviceListFilterRequestParameterSubject).not.toHaveBeenCalled();
    expect(service.getDeviceListRefreshSubject().next).not.toHaveBeenCalled();
  });

  // ==================== DEVICE LIST OPERATIONS TESTS ====================

  it('should load device list successfully with permission', async () => {
    const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };
    const mockResponse = {
      status: 200,
      body: {
        content: [{ id: 1, deviceId: 'DEV001', editable: true, country: 'USA', deviceType: 'TEST', locked: false, productStatus: 'ENABLED' }],
        numberOfElements: 1,
        totalElements: 1
      }
    };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceList.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

    expect(result.success).toBe(true);
    expect(result.devices).toEqual(mockResponse.body.content);
    expect(result.totalDeviceDisplay).toBe(1);
    expect(result.totalDevice).toBe(1);
    expect(result.localDeviceList).toEqual([{
      id: 1, deviceId: 'DEV001', editable: true, country: 'USA', deviceType: 'TEST', locked: false, productStatus: 'ENABLED'
    }]);
  });

  it('should return empty result when no permission', async () => {
    const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };

    permissionService.getDevicePermission.and.returnValue(false);

    const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

    expect(result.success).toBe(false);
    expect(result.devices).toEqual([]);
    expect(result.totalDeviceDisplay).toBe(0);
    expect(result.totalDevice).toBe(0);
    expect(result.localDeviceList).toEqual([]);
  });

  it('should handle non-200 response status', async () => {
    const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };
    const mockResponse = { status: 404, body: null };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceList.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

    expect(result.success).toBe(false);
    expect(result.devices).toEqual([]);
    expect(result.totalDeviceDisplay).toBe(0);
    expect(result.totalDevice).toBe(0);
    expect(result.localDeviceList).toEqual([]);
  });

  it('should handle API error in loadDeviceList', async () => {
    const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceList.and.returnValue(throwError(() => new Error('API Error')));

    const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

    expect(result.success).toBe(false);
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  // ==================== EXPORT CSV TESTS ====================

  it('should export device CSV successfully', async () => {
    const mockDeviceIds = [1, 2, 3];
    const mockGenerateResponse = { body: { fileName: 'devices.csv' } };
    const mockDownloadResponse = { body: 'csv,data' };

    deviceService.generateCSVFileForDevice.and.returnValue(of(mockGenerateResponse as any));
    deviceService.downloadCSVFileForDevice.and.returnValue(of(mockDownloadResponse as any));

    await service.exportDeviceCSV(mockDeviceIds);

    expect(deviceService.generateCSVFileForDevice).toHaveBeenCalledWith(jasmine.any(DeviceExportCSVSearchRequest));
    expect(deviceService.downloadCSVFileForDevice).toHaveBeenCalledWith('devices.csv');
    expect(downloadService.downloadExportCSV).toHaveBeenCalledWith("List_of_Device(s).xls", mockDownloadResponse);
  });

  it('should handle export error', async () => {
    const mockDeviceIds = [1, 2, 3];

    deviceService.generateCSVFileForDevice.and.returnValue(throwError(() => new Error('Export Error')));

    try {
      await service.exportDeviceCSV(mockDeviceIds);
      fail('Expected error to be thrown');
    } catch (error) {
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
    }
  });

  // ==================== DEVICE TYPE CONVERSION TESTS ====================

  it('should convert devices to test type successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [
      { deviceType: 'CLIENT', editable: true, country: 'USA' },
      { deviceType: 'DEMO', editable: true, country: 'USA' }
    ];
    const mockResponse = { status: 200, body: { message: 'Success' } };

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceTypeToTest.and.returnValue(of(mockResponse as any));
    spyOn(service.getDeviceListRefreshSubject(), 'next');

    const result = await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDevicePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
    expect(deviceService.updateDeviceTypeToTest).toHaveBeenCalledWith(mockDeviceIds);
    expect(toastrServiceMock.success).toHaveBeenCalledWith(DEVICE_CONVERT_TO_TEST);
    expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalled();
  });

  it('should show info message when devices are already test type', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [
      { deviceType: 'TEST', editable: true, country: 'USA' },
      { deviceType: 'TEST', editable: true, country: 'USA' }
    ];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);

    const result = await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_TEST);
    expect(deviceService.updateDeviceTypeToTest).not.toHaveBeenCalled();
  });

  it('should return false when validation fails for convert to test', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'CLIENT', editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(false);

    const result = await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.updateDeviceTypeToTest).not.toHaveBeenCalled();
  });

  it('should return false when no permission for convert to test', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'CLIENT', editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(false);

    const result = await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.updateDeviceTypeToTest).not.toHaveBeenCalled();
  });

  it('should handle conversion error for convert to test', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'CLIENT', editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceTypeToTest.and.returnValue(throwError(() => new Error('Conversion Error')));

    try {
      await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);
      fail('Expected error to be thrown');
    } catch (error: any) {
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
    }
  });

  // ==================== CONVERT TO CLIENT TESTS ====================

  it('should convert devices to client type successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'TEST', editable: true, country: 'USA' }];
    const mockResponse = { status: 200, body: { message: 'Success' } };

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceTypeToClient.and.returnValue(of(mockResponse as any));
    spyOn(service.getDeviceListRefreshSubject(), 'next');

    const result = await service.convertDevicesToClient(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDevicePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
    expect(deviceService.updateDeviceTypeToClient).toHaveBeenCalledWith(mockDeviceIds);
    expect(toastrServiceMock.success).toHaveBeenCalledWith(DEVICE_CONVERT_TO_CLIENT);
  });

  it('should show info message when devices are already client type', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [
      { deviceType: 'CLIENT', editable: true, country: 'USA' },
      { deviceType: 'CLIENT', editable: true, country: 'USA' }
    ];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);

    const result = await service.convertDevicesToClient(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_CLIENT);
    expect(deviceService.updateDeviceTypeToClient).not.toHaveBeenCalled();
  });

  // ==================== CONVERT TO DEMO TESTS ====================

  it('should convert devices to demo type successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'TEST', editable: true, country: 'USA' }];
    const mockResponse = { status: 200, body: { message: 'Success' } };

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceTypeToDemo.and.returnValue(of(mockResponse as any));
    spyOn(service.getDeviceListRefreshSubject(), 'next');

    const result = await service.convertDevicesToDemo(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDevicePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
    expect(deviceService.updateDeviceTypeToDemo).toHaveBeenCalledWith(mockDeviceIds);
    expect(toastrServiceMock.success).toHaveBeenCalledWith(DEVICE_CONVERT_TO_DEMO);
  });

  it('should show info message when devices are already demo type', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [
      { deviceType: 'DEMO', editable: true, country: 'USA' },
      { deviceType: 'DEMO', editable: true, country: 'USA' }
    ];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);

    const result = await service.convertDevicesToDemo(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_DEMO);
    expect(deviceService.updateDeviceTypeToDemo).not.toHaveBeenCalled();
  });

  // ==================== LOCK/UNLOCK DEVICE TESTS ====================

  it('should lock/unlock devices successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [
      { locked: false, editable: true, country: 'USA' },
      { locked: false, editable: true, country: 'USA' }
    ];
    const mockResponse = { status: 200, body: { message: 'Devices locked successfully' } };

    spyOn(service, 'validateDeviceLockUnlockPermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceState.and.returnValue(of(mockResponse as any));
    spyOn(service.getDeviceListRefreshSubject(), 'next');

    const result = await service.lockUnlockDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDeviceLockUnlockPermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);
    expect(deviceService.updateDeviceState).toHaveBeenCalledWith(mockDeviceIds, true);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Devices locked successfully');
    expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalled();
  });

  it('should return false when lock/unlock validation fails', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceLockUnlockPermissions').and.returnValue(false);

    const result = await service.lockUnlockDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.updateDeviceState).not.toHaveBeenCalled();
  });

  it('should return false when no lock permission', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceLockUnlockPermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(false);

    const result = await service.lockUnlockDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.updateDeviceState).not.toHaveBeenCalled();
  });

  it('should return false when response status is not 200', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];
    const mockResponse = { status: 400, body: { message: 'Error' } };

    spyOn(service, 'validateDeviceLockUnlockPermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceState.and.returnValue(of(mockResponse as any));

    const result = await service.lockUnlockDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(false);
  });
  // ==================== DEVICE VALIDATION TESTS ====================

  it('should validate device selection with empty device IDs', () => {
    const result = service.validateDeviceSelection([], [], DeviceListResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(Device_Select_Message);
  });

  it('should validate device selection successfully', () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [
      { editable: true, country: 'USA' },
      { editable: true, country: 'USA' }
    ];

    spyOn(service, 'validateUserPermissionsAndCountry').and.returnValue(true);

    const result = service.validateDeviceSelection(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateUserPermissionsAndCountry).toHaveBeenCalledWith(mockSelectedDevices, DeviceListResource);
  });

  it('should validate user permissions and country', () => {
    const mockSelectedDevices = [
      { editable: true, country: 'USA' },
      { editable: true, country: 'Canada' }
    ];

    moduleValidationService.validateWithEditableWithMultipalRecoard.and.returnValue(true);
    spyOn(service, 'validateUserCountryAccess').and.returnValue(true);

    const result = service.validateUserPermissionsAndCountry(mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(moduleValidationService.validateWithEditableWithMultipalRecoard).toHaveBeenCalled();
    expect(service.validateUserCountryAccess).toHaveBeenCalledWith(mockSelectedDevices, DeviceListResource);
  });

  it('should validate user country access', () => {
    const mockSelectedDevices = [
      { editable: true, country: 'USA' },
      { editable: true, country: 'Canada' }
    ];

    moduleValidationService.validateWithUserCountryForMultileRecord.and.returnValue(true);

    const result = service.validateUserCountryAccess(mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(moduleValidationService.validateWithUserCountryForMultileRecord).toHaveBeenCalledWith(['USA', 'Canada'], DeviceListResource, true);
  });

  it('should get device associated countries', () => {
    const mockSelectedDevices = [
      { editable: true, country: 'USA' },
      { editable: true, country: 'Canada' }
    ];

    const result = service.getDeviceAssociatedCountries(mockSelectedDevices);

    expect(result).toEqual(['USA', 'Canada']);
  });

  // ==================== DEVICE DETAIL OPERATIONS TESTS ====================

  it('should load device detail successfully', async () => {
    const mockDeviceId = 123;
    const mockResponse = {
      status: 200,
      body: {
        id: 123,
        deviceId: 'DEV001',
        salesOrderId: 456,
        deviceSerialNo: 'SN001',
        releaseId: 789
      }
    };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceDetail(mockDeviceId);

    expect(result.success).toBe(true);
    expect(result.deviceDetail).toEqual(jasmine.objectContaining({
      id: 123,
      deviceId: 'DEV001',
      salesOrderId: 456,
      deviceSerialNo: 'SN001',
      releaseId: 789
    }));
    expect(result.releaseVersionId).toBe(789);
    expect(result.transferProductDetails).toBeInstanceOf(TransferProductDetails);
  });

  it('should handle device detail with null releaseId', async () => {
    const mockDeviceId = 123;
    const mockResponse = {
      status: 200,
      body: {
        id: 123,
        deviceId: 'DEV001',
        salesOrderId: 456,
        deviceSerialNo: 'SN001',
        releaseId: null
      }
    };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceDetail(mockDeviceId);

    expect(result.success).toBe(true);
    expect(result.releaseVersionId).toBe(-1);
  });

  it('should return empty response when no permission for device detail', async () => {
    const mockDeviceId = 123;

    permissionService.getDevicePermission.and.returnValue(false);

    const result = await service.loadDeviceDetail(mockDeviceId);

    expect(result.success).toBe(false);
    expect(result.deviceDetail).toBe(null);
    expect(result.releaseVersionId).toBe(-1);
    expect(result.transferProductDetails).toBe(null);
  });

  it('should handle API error in device detail', async () => {
    const mockDeviceId = 123;

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceDetail.and.returnValue(throwError(() => new Error('API Error')));

    const result = await service.loadDeviceDetail(mockDeviceId);

    expect(result.success).toBe(false);
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  // ==================== RELEASE VERSION TESTS ====================

  it('should get release versions for test device with permission', async () => {
    const mockResponse = { body: [{ id: 1, version: 'v1.0.0' }] };

    deviceService.getReleaseVersionDetail.and.returnValue(of(mockResponse as any));

    const result = await service.getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, 'v1.0.0', true);

    expect(result.success).toBe(true);
    expect(result.releaseVersions).toEqual(mockResponse.body);
    expect(result.selectedReleaseVersion).toBe(-1);
    expect(result.btnReleaseVersionDisable).toBe(true);
  });

  it('should return empty result for non-test device', async () => {
    const result = await service.getReleaseVersions(deviceTypesEnum.CLIENT_DEVICE, 1, 'v1.0.0', true);

    expect(result.success).toBe(true);
    expect(result.releaseVersions).toEqual([]);
    expect(result.selectedReleaseVersion).toBe(-1);
    expect(result.btnReleaseVersionDisable).toBe(true);
  });

  it('should return empty result when no permission', async () => {
    const result = await service.getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, 'v1.0.0', false);

    expect(result.success).toBe(true);
    expect(result.releaseVersions).toEqual([]);
    expect(result.selectedReleaseVersion).toBe(-1);
    expect(result.btnReleaseVersionDisable).toBe(true);
  });

  it('should return empty result when country ID is null', async () => {
    const result = await service.getReleaseVersions(deviceTypesEnum.TEST_DEVICE, null as any, 'v1.0.0', true);

    expect(result.success).toBe(true);
    expect(result.releaseVersions).toEqual([]);
    expect(result.selectedReleaseVersion).toBe(-1);
    expect(result.btnReleaseVersionDisable).toBe(true);
  });

  it('should assign release version successfully', async () => {
    const mockResponse = { status: 200 };

    deviceService.assignSelectedReleaseVersion.and.returnValue(of(mockResponse as any));

    await service.assignReleaseVersion(123, 456);

    expect(deviceService.assignSelectedReleaseVersion).toHaveBeenCalledWith(jasmine.any(AssignSelectedReleaseVersionRequest));
    expect(toastrServiceMock.success).toHaveBeenCalledWith("Release version assigned successfully");
  });

  it('should handle assign release version error', async () => {
    const mockResponse = { status: 400 };

    deviceService.assignSelectedReleaseVersion.and.returnValue(of(mockResponse as any));

    await service.assignReleaseVersion(123, 456);

    expect(toastrServiceMock.error).toHaveBeenCalledWith("Error in assigning Release version");
  });

  it('should disable assign button correctly', () => {
    expect(service.shouldDisableAssignButton(-1, 123)).toBe(true);
    expect(service.shouldDisableAssignButton(123, 123)).toBe(true);
    expect(service.shouldDisableAssignButton(456, 123)).toBe(false);
  });

  // ==================== FILTER INTEGRATION TESTS ====================

  it('should validate filter form with valid data', () => {
    const formValue = {
      deviceId: 'DEV001',
      deviceSerialNo: null,
      customerName: null,
      packageVersions: null,
      connectionState: null,
      deviceLockState: null,
      deviceEditState: null,
      countries: null,
      drpDeviceType: null,
      salesOrderNumber: null,
      productStatus: null
    };

    commonsService.checkValueIsNullOrEmpty.and.returnValue(false);

    const result = service.validateFilterForm(formValue);

    expect(result).toBe(true);
  });

  it('should validate filter form with all empty data', () => {
    const formValue = {
      deviceId: null,
      deviceSerialNo: null,
      customerName: null,
      packageVersions: null,
      connectionState: null,
      deviceLockState: null,
      deviceEditState: null,
      countries: null,
      drpDeviceType: null,
      salesOrderNumber: null,
      productStatus: null
    };

    commonsService.checkValueIsNullOrEmpty.and.returnValue(true);

    const result = service.validateFilterForm(formValue);

    expect(result).toBe(false);
  });

  it('should build device search request from form data', () => {
    const formValue = {
      deviceId: 'DEV001',
      customerName: 'Test Customer',
      deviceSerialNo: 'SN001',
      countries: [{ id: 1, name: 'USA' }],
      productStatus: 'ENABLED',
      connectionState: 'ONLINE',
      deviceLockState: true,
      deviceEditState: false,
      packageVersions: ['v1.0.0'],
      drpDeviceType: 'TEST_DEVICE',
      salesOrderNumber: ['SO001']
    };

    commonsService.checkNullFieldValue.and.returnValues('DEV001', 'Test Customer', 'SN001', ['v1.0.0'], ['SO001']);
    commonsService.getIdsFromArray.and.returnValue([1]);
    commonsService.getSelectedValueFromEnum.and.returnValues(['ENABLED'], ['ONLINE']);
    commonsService.getSelectedValueFromBooleanKeyValueMapping.and.returnValues(true, false);
    commonsService.getDeviceTypeStringToEnum.and.returnValue('TEST_DEVICE');

    const result = service.buildDeviceSearchRequest(formValue);

    expect(result).toBeInstanceOf(DeviceSearchRequest);
    expect(commonsService.checkNullFieldValue).toHaveBeenCalledWith('DEV001');
    expect(commonsService.getIdsFromArray).toHaveBeenCalledWith([{ id: 1, name: 'USA' }]);
  });

  it('should process filter search successfully', () => {
    const formValue = { deviceId: 'DEV001' };
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

    spyOn(service, 'validateFilterForm').and.returnValue(true);
    spyOn(service, 'buildDeviceSearchRequest').and.returnValue(new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null));
    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    const result = service.processFilterSearch(formValue, false, listingPageReloadSubjectParameter);

    expect(result).toBe(true);
    expect(service.validateFilterForm).toHaveBeenCalledWith(formValue);
    expect(service.buildDeviceSearchRequest).toHaveBeenCalledWith(formValue);
    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalled();
  });

  it('should return false when filter form is invalid', () => {
    const formValue = {};
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

    spyOn(service, 'validateFilterForm').and.returnValue(false);

    const result = service.processFilterSearch(formValue, true, listingPageReloadSubjectParameter);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith('Please provide at least one filter criteria');
  });

  it('should clear all filters and refresh', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    service.clearAllFiltersAndRefresh(listingPageReloadSubjectParameter);

    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
      jasmine.objectContaining({
        deviceSearchRequest: jasmine.objectContaining({
          packageVersions: null
        })
      })
    );
  });

  // ==================== DEVICE OPERATION HANDLER TESTS ====================

  it('should handle device operation for unlock devices', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['lockUnlock']);

    service.handleDeviceOperation(DeviceListOperations.UNLOCK_DEVICES, mockHandler);

    expect(mockHandler.lockUnlock).toHaveBeenCalledWith(false);
  });

  it('should handle device operation for lock devices', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['lockUnlock']);

    service.handleDeviceOperation(DeviceListOperations.LOCK_DEVICES, mockHandler);

    expect(mockHandler.lockUnlock).toHaveBeenCalledWith(true);
  });

  it('should handle device operation for convert to test', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['convertDataToTest']);

    service.handleDeviceOperation(DeviceListOperations.SET_DEVICE_TO_TEST, mockHandler);

    expect(mockHandler.convertDataToTest).toHaveBeenCalled();
  });

  it('should handle device operation for export CSV', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['exportCSV']);

    service.handleDeviceOperation(DeviceListOperations.Export_CSV, mockHandler);

    expect(mockHandler.exportCSV).toHaveBeenCalled();
  });

  it('should handle device operation for transfer device', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['transferDevice']);

    service.handleDeviceOperation(DeviceListOperations.TRANSFER_DEVICE, mockHandler);

    expect(mockHandler.transferDevice).toHaveBeenCalled();
  });

  it('should handle default case for device operation', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['lockUnlock']);

    service.handleDeviceOperation('UNKNOWN_OPERATION', mockHandler);

    expect(mockHandler.lockUnlock).not.toHaveBeenCalled();
  });

  // ==================== ADDITIONAL MISSING COVERAGE TESTS ====================

  it('should handle device operation for convert to client', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['convertDataToClient']);

    service.handleDeviceOperation(DeviceListOperations.SET_DEVICE_TO_CLIENT, mockHandler);

    expect(mockHandler.convertDataToClient).toHaveBeenCalled();
  });

  it('should handle device operation for convert to demo', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['convertDataToDemo']);

    service.handleDeviceOperation(DeviceListOperations.SET_DEVICE_TO_DEMO, mockHandler);

    expect(mockHandler.convertDataToDemo).toHaveBeenCalled();
  });

  it('should handle device operation for sales order association', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['associationDeviceWithSalesOrder']);

    service.handleDeviceOperation(DeviceListOperations.CUSTOMER_SALES_ORDER_ASSOCIATION, mockHandler);

    expect(mockHandler.associationDeviceWithSalesOrder).toHaveBeenCalled();
  });

  it('should handle device operation for edit enable', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['enableDisableDevice']);

    service.handleDeviceOperation(DeviceListOperations.EDIT_ENABLE_DEVICE, mockHandler);

    expect(mockHandler.enableDisableDevice).toHaveBeenCalledWith(true);
  });

  it('should handle device operation for edit disable', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['enableDisableDevice']);

    service.handleDeviceOperation(DeviceListOperations.EDIT_DISABLE_DEVICE, mockHandler);

    expect(mockHandler.enableDisableDevice).toHaveBeenCalledWith(false);
  });

  it('should handle device operation for RMA devices', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['validateProductStatusForRMAAction']);

    service.handleDeviceOperation(DeviceListOperations.RMA_DEVICES, mockHandler);

    expect(mockHandler.validateProductStatusForRMAAction).toHaveBeenCalled();
  });

  it('should handle device operation for disabled devices', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['validateProductStatusForDisableAction']);

    service.handleDeviceOperation(DeviceListOperations.DISABLED_DEVICES, mockHandler);

    expect(mockHandler.validateProductStatusForDisableAction).toHaveBeenCalled();
  });

  it('should reset select element after device operation', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['lockUnlock']);
    const mockSelectElement = { value: '' };
    spyOn(document, 'getElementById').and.returnValue(mockSelectElement as any);

    service.handleDeviceOperation(DeviceListOperations.LOCK_DEVICES, mockHandler, 'customElementId');

    expect(document.getElementById).toHaveBeenCalledWith('customElementId');
    expect(mockSelectElement.value).toBe(DeviceListOperations.DeviceOperations);
  });

  it('should handle missing select element gracefully', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['lockUnlock']);
    spyOn(document, 'getElementById').and.returnValue(null);

    expect(() => {
      service.handleDeviceOperation(DeviceListOperations.LOCK_DEVICES, mockHandler);
    }).not.toThrow();
  });

  // ==================== ENABLE/DISABLE DEVICE TESTS ====================

  it('should enable/disable devices successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];
    const mockResponse = { body: { message: 'Devices enabled successfully' } };

    spyOn(service, 'validateDeviceEnableDisablePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.editEnableDisableForDevice.and.returnValue(of(mockResponse as any));
    spyOn(service.getDeviceListRefreshSubject(), 'next');

    const result = await service.enableDisableDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDeviceEnableDisablePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);
    expect(deviceService.editEnableDisableForDevice).toHaveBeenCalledWith(mockDeviceIds, true);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Devices enabled successfully');
    expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalled();
  });

  it('should return false when enable/disable validation fails', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    spyOn(service, 'validateDeviceEnableDisablePermissions').and.returnValue(false);

    const result = await service.enableDisableDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.editEnableDisableForDevice).not.toHaveBeenCalled();
  });

  it('should return false when no enable/disable permission', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    spyOn(service, 'validateDeviceEnableDisablePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(false);

    const result = await service.enableDisableDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.editEnableDisableForDevice).not.toHaveBeenCalled();
  });

  it('should handle enable/disable error', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    spyOn(service, 'validateDeviceEnableDisablePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.editEnableDisableForDevice.and.returnValue(throwError(() => new Error('Enable/Disable Error')));

    try {
      await service.enableDisableDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);
      fail('Expected error to be thrown');
    } catch (error) {
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
    }
  });

  // ==================== DISABLE PRODUCT STATUS TESTS ====================

  it('should disable product status successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];
    const mockResponse = { body: { message: 'Product status disabled successfully' } };

    spyOn(service, 'validateDeviceDisablePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.disableProductStatusForDevice.and.returnValue(of(mockResponse as any));
    spyOn(service.getDeviceListRefreshSubject(), 'next');

    const result = await service.disableProductStatusForDevices(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDeviceDisablePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
    expect(deviceService.disableProductStatusForDevice).toHaveBeenCalledWith(mockDeviceIds);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Product status disabled successfully');
    expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalled();
  });

  it('should return false when disable validation fails', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceDisablePermissions').and.returnValue(false);

    const result = await service.disableProductStatusForDevices(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.disableProductStatusForDevice).not.toHaveBeenCalled();
  });

  it('should return false when no disable permission', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceDisablePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(false);

    const result = await service.disableProductStatusForDevices(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.disableProductStatusForDevice).not.toHaveBeenCalled();
  });

  it('should handle disable error', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceDisablePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.disableProductStatusForDevice.and.returnValue(throwError(() => new Error('Disable Error')));

    try {
      await service.disableProductStatusForDevices(mockDeviceIds, mockSelectedDevices, DeviceListResource);
      fail('Expected error to be thrown');
    } catch (error: any) {
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
    }
  });

  // ==================== RMA PRODUCT STATUS TESTS ====================

  it('should set RMA product status successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];
    const mockResponse = { body: { message: 'RMA status set successfully' } };

    spyOn(service, 'validateDeviceRMAPermissions').and.returnValue(true);
    deviceService.rmaProductStatusForDevice.and.returnValue(of(mockResponse as any));
    spyOn(service.getDeviceListRefreshSubject(), 'next');

    const result = await service.rmaProductStatusForDevices(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDeviceRMAPermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource, undefined, undefined, undefined, undefined);
    expect(deviceService.rmaProductStatusForDevice).toHaveBeenCalledWith(mockDeviceIds);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('RMA status set successfully');
    expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalled();
  });

  it('should return false when RMA validation fails', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceRMAPermissions').and.returnValue(false);

    const result = await service.rmaProductStatusForDevices(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.rmaProductStatusForDevice).not.toHaveBeenCalled();
  });

  it('should handle RMA error', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceRMAPermissions').and.returnValue(true);
    deviceService.rmaProductStatusForDevice.and.returnValue(throwError(() => new Error('RMA Error')));

    try {
      await service.rmaProductStatusForDevices(mockDeviceIds, mockSelectedDevices, DeviceListResource);
      fail('Expected error to be thrown');
    } catch (error: any) {
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
    }
  });

  // ==================== SALES ORDER ASSOCIATION TESTS ====================

  it('should associate devices with sales order successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];
    const mockSalesOrderData = { id: 123, orderNumber: 'SO001' };
    const mockResponse = { body: { message: 'Devices associated successfully' } };

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    deviceService.associationDeviceWithSalesOrder.and.returnValue(of(mockResponse as any));
    spyOn(service, 'updateSalesOrderCacheOnly').and.returnValue(Promise.resolve());
    spyOn(service.getDeviceListRefreshSubject(), 'next');

    const result = await service.associateDevicesWithSalesOrder(mockDeviceIds, mockSelectedDevices, mockSalesOrderData, true, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDevicePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
    expect(deviceService.associationDeviceWithSalesOrder).toHaveBeenCalledWith(mockDeviceIds, jasmine.any(Object));
    expect(service.updateSalesOrderCacheOnly).toHaveBeenCalled();
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Devices associated successfully');
    expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalled();
  });

  it('should associate devices without updating cache when not new sales order', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];
    const mockSalesOrderData = { id: 123, orderNumber: 'SO001' };
    const mockResponse = { body: { message: 'Devices associated successfully' } };

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    deviceService.associationDeviceWithSalesOrder.and.returnValue(of(mockResponse as any));
    spyOn(service, 'updateSalesOrderCacheOnly').and.returnValue(Promise.resolve());
    spyOn(service.getDeviceListRefreshSubject(), 'next');

    const result = await service.associateDevicesWithSalesOrder(mockDeviceIds, mockSelectedDevices, mockSalesOrderData, false, DeviceListResource);

    expect(result).toBe(true);
    expect(service.updateSalesOrderCacheOnly).not.toHaveBeenCalled();
  });

  it('should return false when association validation fails', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];
    const mockSalesOrderData = { id: 123, orderNumber: 'SO001' };

    spyOn(service, 'validateDevicePermissions').and.returnValue(false);

    const result = await service.associateDevicesWithSalesOrder(mockDeviceIds, mockSelectedDevices, mockSalesOrderData, false, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.associationDeviceWithSalesOrder).not.toHaveBeenCalled();
  });

  it('should handle association error', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];
    const mockSalesOrderData = { id: 123, orderNumber: 'SO001' };

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    deviceService.associationDeviceWithSalesOrder.and.returnValue(throwError(() => new Error('Association Error')));

    try {
      await service.associateDevicesWithSalesOrder(mockDeviceIds, mockSelectedDevices, mockSalesOrderData, false, DeviceListResource);
      fail('Expected error to be thrown');
    } catch (error: any) {
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
    }
  });

  // ==================== VALIDATION METHOD TESTS ====================

  it('should validate single device permissions successfully', () => {
    const mockDevice = { editable: true, country: 'USA' };

    moduleValidationService.validateWithEditStateForSingleRecord.and.returnValue(true);
    moduleValidationService.validateWithUserCountryForSingleRecord.and.returnValue(true);

    const result = service.validateSingleDevicePermissions(mockDevice, DeviceDetailResource);

    expect(result).toBe(true);
    expect(moduleValidationService.validateWithEditStateForSingleRecord).toHaveBeenCalledWith(true, DeviceDetailResource);
    expect(moduleValidationService.validateWithUserCountryForSingleRecord).toHaveBeenCalledWith('USA', DeviceDetailResource, true);
  });

  it('should return false when single device edit state validation fails', () => {
    const mockDevice = { editable: false, country: 'USA' };

    moduleValidationService.validateWithEditStateForSingleRecord.and.returnValue(false);

    const result = service.validateSingleDevicePermissions(mockDevice, DeviceDetailResource);

    expect(result).toBe(false);
    expect(moduleValidationService.validateWithUserCountryForSingleRecord).not.toHaveBeenCalled();
  });

  it('should validate single device country access', () => {
    const mockDevice = { country: 'USA' };

    moduleValidationService.validateWithUserCountryForSingleRecord.and.returnValue(true);

    const result = service.validateSingleDeviceCountryAccess(mockDevice, DeviceDetailResource);

    expect(result).toBe(true);
    expect(moduleValidationService.validateWithUserCountryForSingleRecord).toHaveBeenCalledWith('USA', DeviceDetailResource, true);
  });

  it('should validate device permissions for detail resource', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

    const result = service.validateDevicePermissions(mockDeviceIds, mockSelectedDevices, DeviceDetailResource);

    expect(result).toBe(true);
    expect(service.validateSingleDevicePermissions).toHaveBeenCalledWith(mockSelectedDevices[0], DeviceDetailResource);
  });

  it('should return false for unknown resource in validateDevicePermissions', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    const result = service.validateDevicePermissions(mockDeviceIds, mockSelectedDevices, 'UNKNOWN_RESOURCE');

    expect(result).toBe(false);
  });

  it('should validate device lock/unlock permissions for detail resource with same lock state', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA', locked: true }];

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

    const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_LOCKED);
  });

  it('should validate device lock/unlock permissions for detail resource with different lock state', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA', locked: false }];

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

    const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

    expect(result).toBe(true);
  });

  it('should show unlock message when device is already unlocked', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA', locked: false }];

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

    const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, false, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_UNLOCKED);
  });

  it('should return false for unknown resource in validateDeviceLockUnlockPermissions', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA', locked: false }];

    const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, true, 'UNKNOWN_RESOURCE');

    expect(result).toBe(false);
  });

  it('should validate device enable/disable permissions for list resource', () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateUserCountryAccess').and.returnValue(true);

    const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateUserCountryAccess).toHaveBeenCalledWith(mockSelectedDevices, DeviceListResource);
  });

  it('should validate device enable/disable permissions for detail resource with same editable state', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateSingleDeviceCountryAccess').and.returnValue(true);

    const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_EDIT_ENABLE);
  });

  it('should validate device enable/disable permissions for detail resource with different editable state', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    spyOn(service, 'validateSingleDeviceCountryAccess').and.returnValue(true);

    const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

    expect(result).toBe(true);
  });

  it('should show disable message when device is already disabled', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    spyOn(service, 'validateSingleDeviceCountryAccess').and.returnValue(true);

    const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, false, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_EDIT_DISABLE);
  });

  it('should return false for unknown resource in validateDeviceEnableDisablePermissions', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, 'UNKNOWN_RESOURCE');

    expect(result).toBe(false);
  });

  it('should validate device disable permissions for detail resource', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

    const result = service.validateDeviceDisablePermissions(mockDeviceIds, mockSelectedDevices, DeviceDetailResource);

    expect(result).toBe(true);
    expect(service.validateSingleDevicePermissions).toHaveBeenCalledWith(mockSelectedDevices[0], DeviceDetailResource);
  });

  it('should return false for unknown resource in validateDeviceDisablePermissions', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    const result = service.validateDeviceDisablePermissions(mockDeviceIds, mockSelectedDevices, 'UNKNOWN_RESOURCE');

    expect(result).toBe(false);
  });

  it('should validate device RMA permissions for detail resource with product status validation', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA', productStatus: 'ENABLED' }];
    const mockProductStatusList = [{ key: 'ENABLED', value: 'Enabled' }];
    const mockEnumPipe = { transform: jasmine.createSpy('transform').and.returnValue('Enabled') };
    const mockValidationService = { validateProductStatusForRMAAction: jasmine.createSpy('validateProductStatusForRMAAction').and.returnValue(true) };
    const mockConfirmDialogService = { getErrorMessageDisableToRma: jasmine.createSpy('getErrorMessageDisableToRma') };

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

    const result = service.validateDeviceRMAPermissions(
      mockDeviceIds,
      mockSelectedDevices,
      DeviceDetailResource,
      mockProductStatusList,
      mockEnumPipe,
      mockValidationService,
      mockConfirmDialogService
    );

    expect(result).toBe(true);
    expect(service.validateSingleDevicePermissions).toHaveBeenCalledWith(mockSelectedDevices[0], DeviceDetailResource);
    expect(mockEnumPipe.transform).toHaveBeenCalledWith('ENABLED', mockProductStatusList);
    expect(mockValidationService.validateProductStatusForRMAAction).toHaveBeenCalledWith('Enabled');
  });

  it('should return false when RMA product status validation fails', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA', productStatus: 'DISABLED' }];
    const mockProductStatusList = [{ key: 'DISABLED', value: 'Disabled' }];
    const mockEnumPipe = { transform: jasmine.createSpy('transform').and.returnValue('Disabled') };
    const mockValidationService = { validateProductStatusForRMAAction: jasmine.createSpy('validateProductStatusForRMAAction').and.returnValue(false) };
    const mockConfirmDialogService = { getErrorMessageDisableToRma: jasmine.createSpy('getErrorMessageDisableToRma') };

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

    const result = service.validateDeviceRMAPermissions(
      mockDeviceIds,
      mockSelectedDevices,
      DeviceDetailResource,
      mockProductStatusList,
      mockEnumPipe,
      mockValidationService,
      mockConfirmDialogService
    );

    expect(result).toBe(false);
    expect(mockConfirmDialogService.getErrorMessageDisableToRma).toHaveBeenCalledWith(DeviceDetailResource);
  });

  it('should return false for unknown resource in validateDeviceRMAPermissions', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    const result = service.validateDeviceRMAPermissions(mockDeviceIds, mockSelectedDevices, 'UNKNOWN_RESOURCE');

    expect(result).toBe(false);
  });

  it('should return false when user country access validation fails with empty devices', () => {
    const result = service.validateUserCountryAccess([], DeviceListResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(Device_Select_Message);
  });

  // ==================== RELEASE VERSION ERROR HANDLING TESTS ====================

  it('should handle release version API error', async () => {
    deviceService.getReleaseVersionDetail.and.returnValue(throwError(() => new Error('Release Version Error')));

    const result = await service.getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, 'v1.0.0', true);

    expect(result.success).toBe(true);
    expect(result.releaseVersions).toEqual([]);
    expect(result.selectedReleaseVersion).toBe(-1);
    expect(result.btnReleaseVersionDisable).toBe(true);
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  it('should handle assign release version API error', async () => {
    deviceService.assignSelectedReleaseVersion.and.returnValue(throwError(() => new Error('Assign Error')));

    await service.assignReleaseVersion(123, 456);

    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  // ==================== DEVICE DETAIL NON-200 RESPONSE TESTS ====================

  it('should handle non-200 response in device detail', async () => {
    const mockDeviceId = 123;
    const mockResponse = { status: 404 };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceDetail(mockDeviceId);

    expect(result.success).toBe(false);
    expect(result.deviceDetail).toBe(null);
    expect(result.releaseVersionId).toBe(-1);
    expect(result.transferProductDetails).toBe(null);
  });

  // ==================== PRIVATE METHOD COVERAGE TESTS ====================

  it('should process device list response correctly', async () => {
    const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };
    const mockResponse = {
      status: 200,
      body: {
        content: [
          { id: 1, deviceId: 'DEV001', editable: true, country: 'USA', deviceType: 'TEST', locked: false, productStatus: 'ENABLED' },
          { id: 2, deviceId: 'DEV002', editable: false, country: 'Canada', deviceType: 'CLIENT', locked: true, productStatus: 'DISABLED' }
        ],
        numberOfElements: 2,
        totalElements: 10
      }
    };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceList.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

    expect(result.success).toBe(true);
    expect(result.devices).toEqual(mockResponse.body.content);
    expect(result.totalDeviceDisplay).toBe(2);
    expect(result.totalDevice).toBe(10);
    expect(result.localDeviceList).toEqual([
      { id: 1, deviceId: 'DEV001', editable: true, country: 'USA', deviceType: 'TEST', locked: false, productStatus: 'ENABLED' },
      { id: 2, deviceId: 'DEV002', editable: false, country: 'Canada', deviceType: 'CLIENT', locked: true, productStatus: 'DISABLED' }
    ]);
  });

  it('should handle empty device list response', async () => {
    const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };
    const mockResponse = {
      status: 200,
      body: {
        content: [],
        numberOfElements: 0,
        totalElements: 0
      }
    };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceList.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

    expect(result.success).toBe(true);
    expect(result.devices).toEqual([]);
    expect(result.totalDeviceDisplay).toBe(0);
    expect(result.totalDevice).toBe(0);
    expect(result.localDeviceList).toEqual([]);
  });

  it('should handle null response body in device list', async () => {
    const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };
    const mockResponse = {
      status: 200,
      body: null
    };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceList.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

    expect(result.success).toBe(false);
    expect(result.devices).toEqual([]);
    expect(result.totalDeviceDisplay).toBe(0);
    expect(result.totalDevice).toBe(0);
    expect(result.localDeviceList).toEqual([]);
  });
});
