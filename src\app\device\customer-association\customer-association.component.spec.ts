import { HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of } from 'rxjs';
import { clickOnButtonOrCheckBox, commonsProviders, testErrorHandling } from 'src/app/Tesing-Helper/test-utils';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { BasicSalesOrderDetailResponse } from 'src/app/model/SalesOrder/BasicSalesOrderDetailResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { SSOLoginService } from 'src/app/shared/Service/SSO/ssologin.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { CustomerAssociationComponent } from './customer-association.component';


describe('CustomerAssociationComponent', () => {
  let component: CustomerAssociationComponent;
  let fixture: ComponentFixture<CustomerAssociationComponent>;
  let exceptionHandlingService: ExceptionHandlingService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let salesOrderApiCallServiceMock: jasmine.SpyObj<SalesOrderApiCallService>;



  beforeEach(async () => {
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    salesOrderApiCallServiceMock = jasmine.createSpyObj('SalesOrderApiCallService', ['getBasicSalesOrderDetails']);

    await TestBed.configureTestingModule({
      declarations: [CustomerAssociationComponent],
      imports: [NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule],
      providers: [
        NgbActiveModal,
        CommonsService,
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServiceMock },
        LocalStorageService,
        ExceptionHandlingService,
        SessionStorageService,
        AuthJwtService,
        SSOLoginService,
        CommonOperationsService,
        RoleApiCallService,
        HidePermissionNamePipe,
        CommonsService,
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CustomerAssociationComponent);
    component = fixture.componentInstance;
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should handle sales order workflow and dropdown interactions correctly', async () => {
    // Mock the sales order API call to return empty response
    salesOrderApiCallServiceMock.getBasicSalesOrderDetails.and.returnValue(of(new HttpResponse<BasicSalesOrderDetailResponse>({
      body: null,
      status: 204,
      statusText: "No Content"
    })));
    spyOn(component, 'decline').and.callThrough();

    // Initialize component and trigger lifecycle hooks
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Simulate clicking the add sales order button
    clickOnButtonOrCheckBox(fixture, '#addSalesOrderBtn');

    // Wait for UI to update
    fixture.detectChanges();
    await fixture.whenStable();

    // Simulate closing the sales order field
    clickOnButtonOrCheckBox(fixture, '#closeSalesOrderField');

    // Test country validation in dropdown
    component.onItemClickValidation('country');

    // Test dropdown deselection behavior
    component.onDropdownDeSelect();

    // Test dropdown selection with null value
    component.onDropdownSelect(null);

    // Verify loading state is complete
    expect(component.loading).toBeFalsy();

    // Wait for UI to update
    fixture.detectChanges();
    await fixture.whenStable();

    component.decline();
    expect(component.decline).toHaveBeenCalled();


  });

  it('should call toastrService.error with INTERNAL_SERVER_ERROR', async () => {
    testErrorHandling(salesOrderApiCallServiceMock.getBasicSalesOrderDetails, () => component.onDropdownSelect(null), exceptionHandlingService, toastrServiceMock, fixture);
  });
});
