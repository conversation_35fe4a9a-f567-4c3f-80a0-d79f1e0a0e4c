import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, fakeAsync, TestBed, tick, waitForAsync } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { commonsProviders, getLanguageListResponse, selectAllTestCase, selectOneFromListTestCase, testAuthentication, testToggleFilter } from 'src/app/Tesing-Helper/test-utils';
import { COUNTRY_NOT_SELECTED, ITEMS_PER_PAGE, ListCountryResource, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { CountryPageResponse } from 'src/app/model/Country/CountryPageResponse.model';
import { CountryRequestBody } from 'src/app/model/Country/CountryRequestBody.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { CountryAndLanguageService } from 'src/app/shared/Service/CountryAndLanguageService/country-and-language.service';
import { CountryApiCallService } from 'src/app/shared/Service/CountryService/country-api-call.service';
import { CountryService } from 'src/app/shared/Service/CountryService/country.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { CountryFilterComponent } from '../country-filter/country-filter.component';
import { CreateCountryComponent } from '../create-country/create-country.component';
import { CountryListComponent } from './country-list.component';

describe('CountryListComponent Integration Tests', () => {
  let component: CountryListComponent;
  let fixture: ComponentFixture<CountryListComponent>;
  let authServiceMock: any;
  let countryApiCallServiceMock: any;
  let countryService: CountryService;
  let commonOperationService: CommonOperationsService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let confirmDialogServiceMock: jasmine.SpyObj<ConfirmDialogService>;
  let countryAndLanguageServiceSpy: jasmine.SpyObj<CountryAndLanguageService>;
  let exceptionHandlingService: ExceptionHandlingService;


  const mockCountryListResponse: CountryPageResponse = {
    "content": [{
      "id": 1,
      "country": "United States",
      "languages": ["English", "Italian", "French", "Portuguese", "Spanish"]
    }, {
      "id": 2,
      "country": "Nepal",
      "languages": ["English", "Portuguese"]
    }, {
      "id": 3,
      "country": "Oman",
      "languages": ["English", "French", "Portuguese", "Spanish"]
    }, {
      "id": 4,
      "country": "Philippines",
      "languages": ["English", "Portuguese"]
    }, {
      "id": 5,
      "country": "Trinidad and Tobago",
      "languages": ["English"]
    }, {
      "id": 6,
      "country": "United Arab Emirates",
      "languages": ["English"]
    }, {
      "id": 7,
      "country": "Australia",
      "languages": ["English", "Italian"]
    }, {
      "id": 8,
      "country": "Ireland",
      "languages": ["English", "French", "Portuguese", "Spanish"]
    }, {
      "id": 9,
      "country": "Malta",
      "languages": ["English"]
    }, {
      "id": 10,
      "country": "New Zealand",
      "languages": ["English"]
    }],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 10,
      "sort": {
        "empty": true,
        "sorted": false,
        "unsorted": true
      },
      "offset": 0,
      "paged": true,
      "unpaged": false
    },
    "totalPages": 8,
    "totalElements": 76,
    "last": false,
    "size": 10,
    "number": 0,
    "sort": null,
    "numberOfElements": 10,
    "first": true,
    "empty": false
  }


  beforeEach(waitForAsync(() => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['confirm']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');

    authServiceMock = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    countryApiCallServiceMock = jasmine.createSpyObj('CountryApiCallService', ['getCountryList', 'deleteCountry', 'createCountry']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getCountryPermission']);
    countryAndLanguageServiceSpy = jasmine.createSpyObj('CountryAndLanguageServiceSpy', ['getLanguageList']);


    TestBed.configureTestingModule({
      declarations: [CountryListComponent, PrintListPipe, CountryFilterComponent, PrintListPipe, CreateCountryComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      imports: [NgbPaginationModule, ReactiveFormsModule, FormsModule, NgMultiSelectDropDownModule.forRoot()],
      providers: [
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        SessionStorageService,
        HidePermissionNamePipe,
        CountryService,
        PrintListPipe,
        CommonOperationsService,
        { provide: AuthJwtService, useValue: authServiceMock },
        { provide: CountryApiCallService, useValue: countryApiCallServiceMock },
        CommonsService,
        ExceptionHandlingService,
        { provide: CountryAndLanguageService, useValue: countryAndLanguageServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CountryListComponent);
    countryService = TestBed.inject(CountryService);
    commonOperationService = TestBed.inject(CommonOperationsService);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    component = fixture.componentInstance;
    authServiceMock.isAuthenticate.and.returnValue(true);
    permissionServiceSpy.getCountryPermission?.and.returnValue(true);
    confirmDialogServiceMock.confirm.and.returnValue(Promise.resolve(true));
    fixture.detectChanges();
  });


  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should load countries on init when authenticated', async () => {

    // Arrange: Set up mocks and dependencies
    testAuthentication(authServiceMock, component, fixture);

    // Mock authentication service to return true, indicating the user is authenticated
    authServiceMock.isAuthenticate.and.returnValue(true);

    // Mock the confirmation dialog service to simulate a user confirming positively
    confirmDialogServiceMock.confirm.and.returnValue(Promise.resolve(true));

    // Mock services for language and country API calls with predefined responses
    countryAndLanguageServiceSpy.getLanguageList?.and.returnValue(Promise.resolve(getLanguageListResponse));
    countryApiCallServiceMock.getCountryList.and.returnValue(of(new HttpResponse({ status: 200, body: mockCountryListResponse })));
    countryApiCallServiceMock.createCountry.and.returnValue(of(new HttpResponse({ status: 200, body: { message: "Country added successfully." } })));

    // Mock API response for deleting a Country
    countryApiCallServiceMock.deleteCountry?.and.returnValue(of(new HttpResponse<SuccessMessageResponse>({
      body: { message: 'Country deleted successfully.' }, // Mocked delete success message
      status: 200, // Successful deletion
      statusText: 'OK',
    })));

    // Act: Initialize the component
    component.ngOnInit();

    // Simulate a call to refresh the listing page
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    let countryRequestBody = new CountryRequestBody("United States", "English");

    // Ensure the country service is called correctly with the required parameters
    countryService.callRefreshPageSubject(listingPageReloadSubjectParameter, ListCountryResource, true, countryRequestBody);

    // Wait for any pending asynchronous tasks to complete
    await fixture.whenStable();

    // Update the UI to reflect changes made during initialization
    fixture.detectChanges();

    // Assert: Verify the component state and method calls
    expect(component.dataSizes).toEqual(['10', '25', '50', '100']);
    expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE);
    expect(component.drpselectsize).toBe(ITEMS_PER_PAGE);
    expect(component.page).toBe(1);
    expect(countryApiCallServiceMock.getCountryList).toHaveBeenCalled();
    expect(component.countryResponseList.length).toBe(10);
    expect(component.totalRecord).toBe(76);
    expect(component.loading).toBeFalse();

    // Optional: Verify table row content for a specific use case
    const countryRows = fixture.debugElement.queryAll(By.css('tbody tr'));
    expect(countryRows.length).toBe(10);
    expect(countryRows[0].nativeElement.textContent).toContain('United States');
    expect(countryRows[1].nativeElement.textContent).toContain('Nepal');

    // Update UI elements for checkbox interactions
    fixture.detectChanges();
    component.selectAllCheckboxId = 'selectAllCountry';
    component.showCheckBox = true;
    component.chkPreFix = "country";
    fixture.detectChanges();

    // Test: Select all checkboxes functionality
    selectAllTestCase(fixture, component, '#selectAllCountry');

    // Test: Select a single item from the list and verify selection state
    selectOneFromListTestCase(fixture, component, '#country1country');
    expect(component.selectedCountriesIdList.length).toEqual(1); // Confirm one item is selected
    selectOneFromListTestCase(fixture, component, '#country1country');

    // Test: Simulate dropdown changes and verify toastr messages
    const deleteCountryBtnId = fixture.debugElement.query(By.css('#deleteCountry'));
    await fixture.whenStable();
    fixture.detectChanges();


    // Simulate selecting another invalid operation and verify warning message
    deleteCountryBtnId.nativeElement.click();
    await fixture.whenStable();
    fixture.detectChanges();
    expect(toastrServiceMock.info).toHaveBeenCalledWith(COUNTRY_NOT_SELECTED);

    // Test: Simulate deleting a country and verify success message
    await fixture.whenStable();
    fixture.detectChanges();
    selectOneFromListTestCase(fixture, component, '#country1country');
    deleteCountryBtnId.nativeElement.click();
    await fixture.whenStable();
    fixture.detectChanges();
    expect(toastrServiceMock.success).toHaveBeenCalledWith("Country deleted successfully.");

    // Test: Simulate adding a new country using the "Add Country" button
    const addCountryDebugEl = fixture.debugElement.query(By.css('#AddCountry'));
    const addCountryNativeEl = addCountryDebugEl.nativeElement as HTMLInputElement;

    // Simulate the click on "Add Country"
    addCountryNativeEl.click();
    await fixture.whenStable();
    fixture.detectChanges();

    // Access the dynamically loaded "Create Country" component instance
    const createCountryComponentElement = document.querySelector('app-create-country');

    if (createCountryComponentElement && window['ng']) {
      // Access the Angular component instance
      const createCountryComponent = window['ng'].getComponent(createCountryComponentElement);

      // Spy on the "decline" method of the Create Country component
      spyOn(createCountryComponent, 'decline').and.callThrough();

      // Update form values for creating a new country
      createCountryComponent.createCountryForm.get('countryName').setValue('New Zealand');
      createCountryComponent.createCountryForm.get('language').setValue({
        id: 1,
        name: 'English',
        displayName: 'English',
        shotName: 'en',
      });

      // Reflect the changes on the DOM
      fixture.detectChanges();
      await fixture.whenStable();

      // Simulate the "Upload" button click to create a new country
      const createButton = document.querySelector<HTMLElement>('#uploadBtn');
      if (createButton) {
        createButton.click();
      }

      // Verify the success message for country creation
      expect(toastrServiceMock.success).toHaveBeenCalledWith("Country added successfully.");

      // Simulate the cancel operation
      addCountryNativeEl.click();
      const cancelButton = document.querySelector<HTMLElement>('#countryCancelButton');
      if (cancelButton) {
        cancelButton.click();
      }

      // Verify the "decline" method was called
      expect(createCountryComponent.decline).toHaveBeenCalled();
    }

  });

  it('should load countries on init when authenticated but status code 204', fakeAsync(() => {
    component.filterPageSubjectCallForReloadPage(true, true);
    authServiceMock.isAuthenticate.and.returnValue(true);
    component.ngOnInit();
    countryApiCallServiceMock.getCountryList.and.returnValue(of(new HttpResponse({ status: 204, body: {} })));
    component.loadAll(null)
    fixture.detectChanges();

    // Mock backend call
    tick(); // Simulate async
    fixture.detectChanges();
    expect(component.subscriptionForRoleListFilterRequestParameter).not.toBeNull();
    expect(countryApiCallServiceMock.getCountryList).toHaveBeenCalled();
    expect(component.countryResponseList.length).toBe(0);
    expect(component.totalRecord).toBe(0);
    expect(component.totalRecordDisplay).toBe(0);
    expect(component.loading).toBeFalse();
  }));

  it('should display error message on HTTP error', fakeAsync(() => {
    spyOn(exceptionHandlingService, 'customErrorMessage');
    // Mock the confirmation dialog service to simulate a user confirming positively
    confirmDialogServiceMock.confirm.and.returnValue(Promise.resolve(true));
    component.selectedCountriesIdList = [1, 2, 3];

    // Simulate an HTTP error response
    const httpErrorResponse = new HttpErrorResponse({ status: 500, statusText: 'Internal Server Error' });

    // Use throwError to simulate an HTTP error
    countryApiCallServiceMock.getCountryList.and.returnValue(throwError(() => httpErrorResponse));
    countryApiCallServiceMock.deleteCountry?.and.returnValue(throwError(() => httpErrorResponse));
    // Call loadAll to trigger the error scenario
    component.loadAll(null);
    tick(); // Simulate async
    fixture.detectChanges();

    component.deleteCountry();
    tick(); // Simulate async
    fixture.detectChanges();
    // Check that the customErrorMessage method was called with the error response
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalledWith(httpErrorResponse);
  }));

  it('should call refreshFilter when button is clicked', () => {
    // Arrange: Set up a spy to monitor the 'refreshFilter' method and ensure it is called
    spyOn(component, 'refreshFilter').and.callThrough();

    // Act: Find the button element using its CSS selector and simulate a click event
    const button = fixture.debugElement.query(By.css('#refresh_countryList'));
    if (button) {
      button.nativeElement.click(); // Trigger the click event to invoke the method
    } else {
      console.error('Button not found'); // Log an error if the button is not found in the DOM
    }

    // Assert: Verify that the 'refreshFilter' method was called as expected
    expect(component.refreshFilter).toHaveBeenCalled();
  });

  it('should correctly toggle the filter visibility', async () => {
    // Test setup: Use a helper function to validate the toggle functionality of the filter
    // 'testToggleFilter' ensures the filter visibility state is toggled and validated
    testToggleFilter(component);
  });

  it('should display the correct options in the dropdown change', fakeAsync(() => {
    // Mock the authentication service to simulate an authenticated user
    authServiceMock.isAuthenticate.and.returnValue(true);

    // Trigger the initialization logic for the component
    component.ngOnInit();

    // Arrange: Access the dropdown element in the DOM
    const selectElement = fixture.nativeElement.querySelector('#countryListShowEntry');

    // Act: Simulate the passage of time for any asynchronous operations
    tick(); // Simulate time passage
    fixture.detectChanges(); // Update the view with the current state

    // Select a new value in the dropdown and dispatch the change event
    selectElement.value = selectElement.options[3].value;  // Select the fourth option
    selectElement.dispatchEvent(new Event('change'));

    // Ensure the view reflects the updated dropdown selection
    fixture.detectChanges();

    // Assert: Validate that the selected option updates the component's 'itemsPerPage' value
    expect(component.itemsPerPage).toBe(selectElement.options[3].value);
  }));

  it('should subscribe to loading and country filter parameters on subjectInit', fakeAsync(() => {
    // Spy on the relevant component methods to track their calls
    spyOn(component, 'setLoadingStatus');
    spyOn(component, 'resetPage');

    // Arrange: Define parameters for refreshing the country filter and listing page
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    let countryRequestBody = new CountryRequestBody("United States", "English");

    // Act: Simulate the call to refresh the country filter through the service
    countryService.callRefreshPageSubject(listingPageReloadSubjectParameter, ListCountryResource, true, countryRequestBody);

    // Simulate the passage of time to resolve asynchronous operations
    tick();

    // Trigger change detection to update the view and execute pending lifecycle hooks
    fixture.detectChanges();

    // Assert: Verify that the component's methods were called with the expected parameters
    expect(component.setLoadingStatus).toHaveBeenCalledWith(true); // Ensure loading status was updated
    expect(component.resetPage).toHaveBeenCalledWith(); // Ensure the page reset logic was executed
  }));

  it('should unsubscribe from subscriptions on destroy', () => {
    // Spy on the unsubscribe method of the subscription to ensure it's called
    spyOn(component.subscriptionForRoleListFilterRequestParameter, 'unsubscribe');

    // Act: Call the component's ngOnDestroy method
    component.ngOnDestroy();

    // Assert: Verify that the unsubscribe method was called
    expect(component.subscriptionForRoleListFilterRequestParameter.unsubscribe).toHaveBeenCalled();
  });

  it('should correctly update page number on page change', () => {
    // Spy on the 'loadPage' method to ensure it is called during the page change
    spyOn(component, 'loadPage').and.callThrough();

    // Arrange: Locate the pagination element in the template
    const paginationElement = fixture.debugElement.query(By.css('ngb-pagination'));

    // Act: Simulate a page change event to page 2
    paginationElement.triggerEventHandler('pageChange', 2);

    // Ensure the view reflects the changes
    fixture.detectChanges();

    // Assert: Verify that the page number in the component is updated correctly
    expect(component.page).toBe(2); // Page should be updated to 2
  });

  it('should render app-country-filter with correct inputs', () => {
    // Initialize component and trigger change detection
    component.ngOnInit(); // Component initialization logic is executed
    fixture.detectChanges(); // Update the DOM and synchronize the component state

    // Access the app-country-filter child component instance
    const filterComponent = fixture.debugElement.query(By.directive(CountryFilterComponent)).componentInstance;
    expect(filterComponent).toBeTruthy(); // Ensure the filter component is rendered correctly

    // Set values on the filter form inside the filter component
    filterComponent.filterCountryForm.get('countryName').setValue('India'); // Set 'countryName' field to 'India'
    filterComponent.filterCountryForm.get('languageName').setValue('English'); // Set 'languageName' field to 'English'

    // Spy on relevant methods to verify their invocation
    spyOn(countryService, 'callCountryListFilterRequestParameterSubject').and.callThrough();
    spyOn(component, 'loadAll').and.callThrough();

    // Trigger change detection after updating form values
    fixture.detectChanges();

    // Simulate a click on the filter search element (ensure the correct ID is used)
    const selectElement = fixture.nativeElement.querySelector('#countryFilterSearch');
    selectElement.click(); // Simulate user interaction by triggering a click event

    // Assert: Verify the service and component methods were called as expected
    expect(countryService.callCountryListFilterRequestParameterSubject).toHaveBeenCalled(); // Confirm service method is called
    expect(component.loadAll).toHaveBeenCalledWith(
      jasmine.objectContaining({ country: 'India', language: 'English' }) // Check parameters passed to 'loadAll'
    );
  });

  it('should render app-country-filter with correct inputs are blank', () => {
    // Initialize the component and trigger change detection to ensure the view is set up
    component.ngOnInit();
    fixture.detectChanges();

    // Access the instance of the CountryFilterComponent within the test fixture
    const filterComponent = fixture.debugElement.query(By.directive(CountryFilterComponent)).componentInstance;

    // Ensure the filter component instance is truthy (i.e., it exists and is initialized correctly)
    expect(filterComponent).toBeTruthy();

    // Spy on the service methods to monitor their calls during the test execution
    spyOn(countryService, 'callCountryListFilterRequestParameterSubject').and.callThrough();
    spyOn(commonOperationService, 'showEmptyFilterTosteMessge').and.callThrough();

    // Trigger change detection again to ensure the view updates with any changes
    fixture.detectChanges();

    // Simulate a user interaction by clicking the select element identified by its ID
    const selectElement = fixture.nativeElement.querySelector('#countryFilterSearch');
    selectElement.click();

    // Verify that the service method to show the empty filter message is called upon interaction
    expect(commonOperationService.showEmptyFilterTosteMessge).toHaveBeenCalled();

    // Verify that the toastr service displays the correct message for an empty filter selection
    expect(toastrServiceMock.info).toHaveBeenCalledWith("Please Select Filter To Search");
  });


  it('should render app-country-filter with correct inputs are invalid', () => {
    // Initialize the component and trigger change detection to ensure the view is set up
    component.ngOnInit();
    fixture.detectChanges();

    // Access the instance of the CountryFilterComponent within the test fixture
    const filterComponent = fixture.debugElement.query(By.directive(CountryFilterComponent)).componentInstance;

    // Ensure the filter component instance is truthy (i.e., it exists and is initialized correctly)
    expect(filterComponent).toBeTruthy();

    // Access the form controls and set invalid values to simulate user input
    filterComponent.filterCountryForm.get('countryName').setValue('A'.repeat(SMALL_TEXTBOX_MAX_LENGTH + 50)); // Exceeding max length
    filterComponent.filterCountryForm.get('languageName').setValue('English'); // Valid value for languageName

    // Mark the 'countryName' control as touched and dirty to trigger form validation checks
    filterComponent.filterCountryForm.get('countryName').markAsTouched();
    filterComponent.filterCountryForm.get('countryName').markAsDirty();

    // Trigger change detection again to ensure the form updates based on the new values and validation status
    fixture.detectChanges();

    // Access the select element and the error message in the template
    const selectElement = fixture.nativeElement.querySelector('#countryFilterSearch');
    const errorMessaage = fixture.nativeElement.querySelector("#countryMaxLengthError").textContent;

    // Check if the select element is disabled due to invalid form input (countryName exceeding max length)
    const isDisabled = selectElement.hasAttribute('disabled');

    // Assert that the select element is disabled when the form is invalid
    expect(isDisabled).toBeTrue();

    // Assert that the correct error message is displayed for the countryName field
    expect(errorMessaage).toEqual(SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE);
  });


  it('should render app-country-filter with correct inputs', () => {
    // Simulate setting form values before ngOnInit is called
    component.countryRequestBody = new CountryRequestBody("United States", "English");
    component.isFilterComponentInitWithApicall = false;

    // Access the instance of the CountryFilterComponent within the test fixture
    const filterComponent = fixture.debugElement.query(By.directive(CountryFilterComponent)).componentInstance;

    // Ensure the filter component instance is truthy (i.e., it exists and is initialized correctly)
    expect(filterComponent).toBeTruthy();

    // Trigger change detection to ensure the view reflects the updated inputs
    fixture.detectChanges();

    // Verify that the countryRequestBody in the filter component matches the simulated value set in the parent component
    expect(filterComponent.countryRequestBody).toEqual(component.countryRequestBody);

    // Verify that the isFilterComponentInitWithApicall in the filter component matches the value set in the parent component
    expect(filterComponent.isFilterComponentInitWithApicall).toBe(component.isFilterComponentInitWithApicall);
  });


  it('should render app-country-filter click on clear button', () => {
    // Initialize the component and trigger change detection to ensure the view is set up
    component.ngOnInit();
    fixture.detectChanges();

    // Access the instance of the CountryFilterComponent within the test fixture
    const filterComponent = fixture.debugElement.query(By.directive(CountryFilterComponent)).componentInstance;

    // Ensure the filter component instance is truthy (i.e., it exists and is initialized correctly)
    expect(filterComponent).toBeTruthy();

    // Trigger change detection again to ensure the component reflects the latest state
    fixture.detectChanges();

    // Simulate a user click on the clear button (refresh button with ID 'countryRefreshBtn')
    const selectElement = fixture.nativeElement.querySelector('#countryRefreshBtn');
    selectElement.click();

    // Verify that the form controls were cleared after clicking the refresh/clear button
    // This confirms that the form was reset to its initial state after clearing
    expect(filterComponent.filterCountryForm.get('countryName').value).toEqual('');
    expect(filterComponent.filterCountryForm.get('languageName').value).toEqual('');
  });


});
