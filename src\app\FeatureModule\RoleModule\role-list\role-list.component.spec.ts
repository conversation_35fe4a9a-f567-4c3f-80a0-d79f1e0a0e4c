import { CommonModule } from '@angular/common';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgbPagination } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { INTERNAL_SERVER_ERROR, ITEMS_PER_PAGE } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { Pageable } from 'src/app/model/common/pageable.model';
import { Sort } from 'src/app/model/common/sort.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { RolePageResponse } from 'src/app/model/Role/rolePageResponse.model';
import { RolePermissionResponse } from 'src/app/model/Role/rolePermissionResponse.model';
import { RoleResponse } from 'src/app/model/Role/roleResponse.model';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { GetRolePermissionName } from 'src/app/shared/pipes/getRolePermissionName.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { GetPermissionModuleName } from 'src/app/shared/pipes/Role/getPermissionModuleName.pipe';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { RoleService } from 'src/app/shared/Service/RoleService/role.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { commonsProviders, rolePermissions, selectAllTestCase, testAuthentication, testDropdownInteraction, testPagination, testToggleFilter } from 'src/app/Tesing-Helper/test-utils';
import { RoleDetailComponent } from '../role-detail/role-detail.component';
import { RolefilterComponent } from '../rolefilter/rolefilter.component';
import { RoleListComponent } from './role-list.component';

describe('RoleListComponent', () => {
  let component: RoleListComponent;
  let fixture: ComponentFixture<RoleListComponent>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let exceptionHandlingService: ExceptionHandlingService;
  let roleApiCallService: RoleApiCallService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let roleServiceSpy: RoleService;
  let confirmDialogServiceMock: jasmine.SpyObj<ConfirmDialogService>;

  const sort = new Sort(true, false, false); // Unsigned, not sorted, not empty
  // Create the Pageable object
  const pageable = new Pageable(sort, 0, 10, 0, true, false);

  const roleResponse: RoleResponse = new RoleResponse(72, "Device / SV Team", null, [
    new RolePermissionResponse(30, "Device Log Reader", "Read / Download the Logs uploaded from Device", "LOGS"),
    new RolePermissionResponse(31, "Job Reader", "Read Job Information (Listing and Detail Page)", "JOBS"),
    new RolePermissionResponse(7, "Probe Admin", "Manage all Probe actions and explicitly user will get the Probe Reader Permission", "PROBES"),
    new RolePermissionResponse(11, "Software Build Admin", "Manage all Software Build actions and explicitly user will get the Software Build Reader Permission.", "SOFTWARE BUILDS"),
    new RolePermissionResponse(26, "Video Admin", "Manage the Videos and explicitly user will get the Video Reader Permission", "VIDEOS")
  ]
  )

  const content: Array<RoleResponse> = [new RoleResponse(74, "Cloud Admin", null, [
    new RolePermissionResponse(67, "Active/Inactive Country", "Permission used to mark the country as active or inactive.", "COUNTRY"),
    new RolePermissionResponse(66, "Add Country", "Permission used to add new country", "COUNTRY"),
    new RolePermissionResponse(48, "Audit Reader", "Read the Audit Logs (Listing and View Activity Pop-Up)", "AUDIT"),
    new RolePermissionResponse(41, "Bridge Kit Management Admin", "Manage the Bridge Kits via CSV import and explicitly user will get the Bridge Kit Management Reader Permission", "KIT MANAGEMENT"),
    new RolePermissionResponse(68, "Country Admin", "Manage the Country and explicitly user will get all country permission", "COUNTRY"),
    new RolePermissionResponse(32, "Country Reader", "Read the Country Information", "COUNTRY"),
    new RolePermissionResponse(1, "Device Admin", "Manage all Device actions and explicitly user will get the Device Reader Permission", "DEVICES"),
    new RolePermissionResponse(30, "Device Log Reader", "Read / Download the Logs uploaded from Device", "LOGS"),
    new RolePermissionResponse(31, "Job Reader", "Read Job Information (Listing and Detail Page)", "JOBS"),
    new RolePermissionResponse(64, "OTS Kit Management Admin", "Manage the OTS Kits via CSV import and explicitly user will get the OTS Kit Management Reader Permission", "KIT MANAGEMENT"),
    new RolePermissionResponse(7, "Probe Admin", "Manage all Probe actions and explicitly user will get the Probe Reader Permission", "PROBES"),
    new RolePermissionResponse(63, "Probe Config Group Admin", "Manage all Probe Config Group actions and explicitly user will get the Probe Config Group Permission", "PROBE CONFIG GROUP"),
    new RolePermissionResponse(59, "Probe Config Group Reader", "Read Probe Config Group (Listing and Detail Page)", "PROBE CONFIG GROUP"),
    new RolePermissionResponse(18, "Role Admin", "Manage all Role actions and explicitly user will get Role Reader Permission.", "ROLE"),
    new RolePermissionResponse(44, "Rolr- Admin", "Manage all Rolractions and explicitly user will get RolrReader Permission.", "SALES ORDER"),
    new RolePermissionResponse(11, "Software Build Admin", "Manage all Software Build actions and explicitly user will get the Software Build Reader Permission.", "SOFTWARE BUILDS"),
    new RolePermissionResponse(22, "User Admin", "Manage all User actions and explicitly user will get the User Reader Permission", "USERS"),
    new RolePermissionResponse(26, "Video Admin", "Manage the Videos and explicitly user will get the Video Reader Permission", "VIDEOS")
  ]), roleResponse
  ];

  let roleListResponse: RolePageResponse = new RolePageResponse(pageable, 39, false, 383, 10, true, sort, 10, 0, false, content);

  let roleDetailDisplayResponse: RoleResponse = roleResponse;


  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['confirm']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getRolePermission', 'getPermissionForPermissionList',]);

    await TestBed.configureTestingModule({
      declarations: [RoleListComponent, GetPermissionModuleName, GetRolePermissionName, RolefilterComponent, PrintListPipe, RoleDetailComponent, HidePermissionNamePipe],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      imports: [NgbPagination, FormsModule, ReactiveFormsModule, NgMultiSelectDropDownModule.forRoot(), CommonModule],
      providers: [
        SessionStorageService,
        CommonsService,
        CommonOperationsService,
        PrintListPipe,
        HidePermissionNamePipe,
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        RoleApiCallService,
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: AuthJwtService, useValue: authServiceSpy },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(RoleListComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    roleServiceSpy = TestBed.inject(RoleService);
    roleApiCallService = TestBed.inject(RoleApiCallService);
    component = fixture.componentInstance;
    fixture.detectChanges();
    confirmDialogServiceMock.confirm.and.returnValue(Promise.resolve(true));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {

    it('should initialize the component when the user is authenticated', async () => {

      // Simulate user authentication
      authServiceSpy.isAuthenticate?.and.returnValue(true);

      // Simulate user permissions for accessing Role functionality
      permissionServiceSpy.getRolePermission?.and.returnValue(true);

      component.ngOnInit();
      // **Act: Trigger Angular lifecycle methods and finalize change detection**
      fixture.detectChanges(); // Run initial change detection
      await fixture.whenStable(); // Wait for asynchronous operations to complete

      spyOn(component, 'clickOnRefreshButton')?.and.callThrough();
      const button = fixture.nativeElement.querySelector('#refresh_roleList');
      expect(button).toBeTruthy();
      button?.click();
      expect(component.clickOnRefreshButton).toHaveBeenCalled();

      testAuthentication(authServiceSpy, component, fixture);

      // Test dropdown interaction
      testDropdownInteraction(fixture, component, '#roleListShowEntry');

      // Test pagination
      testPagination(fixture, component, '#roleList-pagination', 2);
    });

    // Test: Toggles filter visibility and updates the button text
    it('should toggle filter visibility and update the button text', () => {
      // Use the generic utility to test the toggle behavior
      testToggleFilter(component);
    });

    // Test: Displays an error message when an internal server error occurs
    it('should display an error message when an internal server error occurs', () => {
      // Arrange: Simulate a 500 Internal Server Error
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
      spyOn(roleApiCallService, 'getRoleList')?.and.returnValue(throwError(() => mockError));
      spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

      // Act: Trigger the data loading method
      component.loadAll(null);

      // Assert: Verify the error handling logic is triggered
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    });

    // Test: Handles an error response when loading data
    it('should handle error response in the LoadAll method', () => {
      // Arrange: Simulate a 500 status response with no body
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      spyOn(roleApiCallService, 'getRoleList')?.and.returnValue(of(new HttpResponse<RolePageResponse>({
        body: null,
        status: 500,
        statusText: 'OK',
      })));

      // Act: Call `loadAll` to attempt data loading
      component.loadAll(null);

      // Assert: Verify no data is loaded and error state is managed
      expect(component.roleResponseList.length).toEqual(0);
      expect(component.totalRecordDisplay).toEqual(0);
      expect(component.totalRecord).toEqual(0);
      expect(component.loading).toBeFalse();
    });
  });

  it('should initialize form controls on ngOnInit role List', async () => {
    // **Arrange: Setup the required dependencies and initial state**

    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Simulate user permissions for accessing Role functionality
    permissionServiceSpy.getRolePermission?.and.returnValue(true);

    // Mock API call to fetch the Role list and return a successful response
    spyOn(roleApiCallService, 'getRoleList')?.and.returnValue(of(new HttpResponse<RolePageResponse>({
      body: roleListResponse, // Mocked response data
      status: 200, // Simulate a successful API response
    })));

    // Mock API call to fetch Role details and return a successful response
    spyOn(roleApiCallService, 'getRoleDetail')?.and.returnValue(of(new HttpResponse<RoleResponse>({
      body: roleDetailDisplayResponse, // Mocked response data
      status: 200, // Simulate a successful API response
    })));

    // Mock API call to fetch Role permissions and return a successful response
    spyOn(roleApiCallService, 'getRolePermissionList')?.and.returnValue(Promise.resolve(rolePermissions));

    // Mock API response for deleting a Role
    spyOn(roleApiCallService, 'deleteRole')?.and.returnValue(of(new HttpResponse<SuccessMessageResponse>({
      body: { message: 'Role deleted successfully.' }, // Mocked delete success message
      status: 200, // Successful deletion
      statusText: 'OK',
    })));

    // Mock API response for updating a Role
    spyOn(roleApiCallService, 'updateRole')?.and.returnValue(of(new HttpResponse<SuccessMessageResponse>({
      body: { message: 'Role updated successfully.' }, // Mocked update success message
      status: 200, // Successful update
      statusText: 'OK',
    })));

    // Spy on service and component methods to track their invocation
    spyOn(roleServiceSpy, 'callRoleListFilterRequestParameterSubject')?.and.callThrough();
    spyOn(component, 'loadAll')?.and.callThrough();

    // Initialize the component (triggers ngOnInit and sets up component state)
    component.ngOnInit();
    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution

    // Access the child filter component instance for validation
    const filterDebugElement = fixture.debugElement.query(By.css('app-rolefilter'));
    const filterComponent = filterDebugElement.componentInstance;

    // **Assert: Validate the state of the filter component**
    expect(filterComponent).toBeTruthy(); // Ensure the child filter component is rendered

    // Verify that form controls in the filter component are initialized with default values
    expect(filterComponent.filterRoleForm?.get('roleName').value).toEqual(''); // Default value for `roleName`
    expect(filterComponent.filterRoleForm?.get('permission').value).toEqual([]); // Default value for `permission`

    // **Act: Trigger Angular lifecycle methods and finalize change detection**
    fixture.detectChanges();
    await fixture.whenStable();

    // Verify permissions and pagination-related properties
    expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE); // Items per page should match the configured constant
    expect(component.drpselectsize).toBe(ITEMS_PER_PAGE); // Dropdown size should match the configured constant
    expect(component.previousPage).toBe(1); // Default page should be the first page
    expect(component.dataSizes).toEqual(['10', '25', '50', '100']); // Data sizes for pagination dropdown

    // Verify that the service method was called to initialize filter parameters
    expect(roleServiceSpy.callRoleListFilterRequestParameterSubject).toHaveBeenCalled();

    // Assert that the component state reflects the API response data
    expect(component.totalItems).toEqual(roleListResponse.totalElements); // Total items should match the API response
    expect(component.roleResponseList).toEqual(roleListResponse.content); // List should populate with the response content
    expect(component.totalRecord).toEqual(roleListResponse.totalElements); // Total records should match the response
    expect(component.totalRecordDisplay).toEqual(roleListResponse.numberOfElements); // Displayed records should match the response

    // Trigger a search without selecting any filter to test error handling
    const roleFilterSearch = fixture.debugElement.query(By.css('#roleFilterSearch'));
    roleFilterSearch.nativeElement.click();
    fixture.detectChanges();
    await fixture.whenStable();
    expect(toastrServiceMock.info).toHaveBeenCalledWith("Please Select Filter To Search");

    // Update form controls and simulate search
    spyOn(filterComponent, 'roleListPageRefresh').and.callThrough();
    filterComponent.filterRoleForm?.get('roleName').setValue('Role');
    fixture.detectChanges();
    await fixture.whenStable();
    roleFilterSearch.nativeElement.click();
    expect(filterComponent.roleListPageRefresh).toHaveBeenCalled();

    // Simulate navigation to Role detail view
    component.isFilterHidden = true;
    filterComponent.listPageRefreshForbackToDetailPage = true;
    filterComponent.ngOnInit();
    const selectElementDetails = fixture.debugElement.query(By.css('[id="72"]'));
    selectElementDetails.nativeElement.click();
    fixture.detectChanges();
    await fixture.whenStable();

    // Access the child Detail component instance for validation
    const detailComponent = fixture.debugElement.query(By.directive(RoleDetailComponent)).componentInstance;
    expect(detailComponent).toBeTruthy();
    spyOn(detailComponent, 'refreshRoleDetailPage').and.callThrough();

    const DetailRefresh = fixture.debugElement.query(By.css('#roleDetailRefresh'));
    DetailRefresh.nativeElement.click();

    expect(detailComponent.refreshRoleDetailPage).toHaveBeenCalled();

    // Spy on the method `deleteRoleConfirmation` to use its real implementation
    spyOn(roleApiCallService, 'deleteRoleConfirmation').and.callThrough();

    const roleDetailOperation = fixture.nativeElement.querySelector('#roleOperation');
    if (roleDetailOperation) {
      roleDetailOperation.value = roleDetailOperation?.options[1]?.value; // Select "100"
      roleDetailOperation.dispatchEvent(new Event('change'));
    }


    // Access the dynamically loaded "Create Country" component instance
    const createAndUpdateRoleComponentElement = document.querySelector('app-create-and-update-role');

    if (createAndUpdateRoleComponentElement && window['ng']) {
      // Access the Angular component instance
      const createAndUpdateRoleComponent = window['ng'].getComponent(createAndUpdateRoleComponentElement);

      // Spy on the "decline" method of the Create Country component
      spyOn(createAndUpdateRoleComponent, 'decline').and.callThrough();

      // Update form values for creating a new country
      createAndUpdateRoleComponent.createRoleForm.get('permission').setValue([{
        id: 48,
        name: "Audit Reader",
        description: "Read the Audit Logs (Listing and View Activity Pop-Up)",
        module: "AUDIT"
      }]);

      // Reflect the changes on the DOM
      fixture.detectChanges();
      await fixture.whenStable();

      // Simulate the "Upload" button click to create a new country
      const createButton = document.querySelector<HTMLElement>('#uploadBtn');
      if (createButton) {
        createButton.click();
      }

      // Verify the success message for country creation
      expect(toastrServiceMock.success).toHaveBeenCalledWith("Role updated successfully.");

      // Simulate the cancel operation
      if (roleDetailOperation) {
        roleDetailOperation.value = roleDetailOperation?.options[1]?.value; // Select "100"
        roleDetailOperation.dispatchEvent(new Event('change'));
      }
      const cancelButton = document.querySelector<HTMLElement>('#roleCancelButton');
      if (cancelButton) {
        cancelButton.click();
      }

      // Verify the "decline" method was called
      expect(createAndUpdateRoleComponent.decline).toHaveBeenCalled();
    }

    // Reflect the changes on the DOM
    fixture.detectChanges();
    await fixture.whenStable();

    if (roleDetailOperation) {
      roleDetailOperation.value = roleDetailOperation?.options[2].value;
      roleDetailOperation.dispatchEvent(new Event('change'));
    }
    // **Act: Trigger Angular lifecycle methods and finalize change detection**
    fixture.detectChanges(); // Run initial change detection
    await fixture.whenStable(); // Wait for asynchronous operations to complete


    expect(toastrServiceMock.success).toHaveBeenCalledWith('Role deleted successfully.');

    // **Act: Trigger Angular lifecycle methods and finalize change detection**
    fixture.detectChanges(); // Run initial change detection
    await fixture.whenStable(); // Wait for asynchronous operations to complete

    selectAllTestCase(fixture, component, `#${component.selectAllCheckboxId}`);

    spyOn(component, 'selectCheckbox').and.callThrough();

    const selectOneCheckBox = fixture.nativeElement.querySelector(`#${component.chkPreFix + roleListResponse.content[0].id + component.chkPreFix}`);
    selectOneCheckBox.click();
    expect(component.selectCheckbox).toHaveBeenCalled();

    const roleListOperation = fixture.nativeElement.querySelector('#roleOperation');
    if (roleListOperation) {
      roleListOperation.value = roleListOperation?.options[2]?.value; // Select "100"
      roleListOperation.dispatchEvent(new Event('change'));
    }
    // **Act: Trigger Angular lifecycle methods and finalize change detection**
    fixture.detectChanges(); // Run initial change detection
    await fixture.whenStable(); // Wait for asynchronous operations to complete

    expect(toastrServiceMock.success).toHaveBeenCalledWith('Role deleted successfully.');

    selectOneCheckBox.click();

    // **Act: Trigger Angular lifecycle methods and finalize change detection**
    fixture.detectChanges(); // Run initial change detection
    await fixture.whenStable(); // Wait for asynchronous operations to complete

    selectOneCheckBox.click();

    expect(component.selectedRoleIdList.length).toEqual(0);
  });

  it("create role ", async () => {
    // **Arrange: Setup the required dependencies and initial state**
    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Simulate user permissions for accessing Rolefunctionality
    permissionServiceSpy.getRolePermission?.and.returnValue(true);

    // Mock API call to fetch the Role Detail and return a successful response
    spyOn(roleApiCallService, 'getRolePermissionList')?.and.returnValue(Promise.resolve(rolePermissions));

    // Mock API response for creating a Role
    spyOn(roleApiCallService, 'createRole')?.and.returnValue(of(new HttpResponse<SuccessMessageResponse>({
      body: { message: 'Role Create successfully.' }, // Mocked delete success message
      status: 200, // Successful deletion
      statusText: 'OK',
    })));
    component.ngOnInit();
    fixture.detectChanges();

    const addRoleButton = fixture.nativeElement.querySelector('#AddRoleList');
    addRoleButton.click();

    // Reflect the changes on the DOM
    fixture.detectChanges();
    await fixture.whenStable();

    const createAndUpdateRoleComponentElement1 = document.querySelector('app-create-and-update-role');

    if (createAndUpdateRoleComponentElement1 && window['ng']) {
      // Access the Angular component instance
      const createAndUpdateRoleComponent = window['ng'].getComponent(createAndUpdateRoleComponentElement1);

      createAndUpdateRoleComponent.createRoleForm.reset();

      // Reflect the changes on the DOM
      fixture.detectChanges();
      await fixture.whenStable();

      createAndUpdateRoleComponent.createRoleForm.get('roleName').setValue("DummyRole")
      // Update form values for creating a new country
      createAndUpdateRoleComponent.createRoleForm.get('permission').setValue([{
        id: 48,
        name: "Audit Reader",
        description: "Read the Audit Logs (Listing and View Activity Pop-Up)",
        module: "AUDIT"
      }]);

      // Reflect the changes on the DOM
      fixture.detectChanges();
      await fixture.whenStable();

      // Simulate the "Upload" button click to create a new country
      const createButton = document.querySelector<HTMLElement>('#uploadBtn');
      if (createButton) {
        createButton.click();
      }
      // Reflect the changes on the DOM
      fixture.detectChanges();
      await fixture.whenStable();
      // Verify the success message for country creation
      expect(toastrServiceMock.success).toHaveBeenCalledWith('Role Create successfully.');
    }

  })

});

