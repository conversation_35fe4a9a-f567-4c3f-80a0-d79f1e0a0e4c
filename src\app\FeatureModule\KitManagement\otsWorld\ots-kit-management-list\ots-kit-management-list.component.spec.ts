import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgbPagination } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { INTERNAL_SERVER_ERROR, ITEMS_PER_PAGE } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { ProbeConfigGroupDetailComponent } from 'src/app/FeatureModule/ProbeConfigGroup/probe-config-group-detail/probe-config-group-detail.component';
import { Pageable } from 'src/app/model/common/pageable.model';
import { OTSKitManagementDetailResponse } from 'src/app/model/KitManagement/otsWorld/OTSKitManagementDetailResponse.model';
import { OTSKitManagementListResponse } from 'src/app/model/KitManagement/otsWorld/OTSKitManagementListResponse.model';
import { OTSKitManagementPageResponse } from 'src/app/model/KitManagement/otsWorld/OTSKitManagementPageResponse.model';
import { Sort } from 'src/app/model/sort.model';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { OtsKitManagemantApiCallService } from 'src/app/shared/Service/KitManagemant/ots-kit-managemant-api-call.service';
import { OtsKitManagemantService } from 'src/app/shared/Service/KitManagemant/ots-kit-managemant.service';
import { commonsProviders, countryListResponse, operationsListTestcase, testAuthentication, testDropdownInteraction, testPagination, testToggleFilter } from 'src/app/Tesing-Helper/test-utils';
import { OtsKitManagementDetailComponent } from '../ots-kit-management-detail/ots-kit-management-detail.component';
import { OtsKitManagementFilterComponent } from '../ots-kit-management-filter/ots-kit-management-filter.component';
import { OtsKitManagementListComponent } from './ots-kit-management-list.component';

let sort: Sort = new Sort(true, false, true);
let pageable = new Pageable(sort, 0, 10, 0, true, false);

let otsKitList = [
  new OTSKitManagementListResponse(
    116,
    "India",
    "P008383-002",
    "P007785-2021",
    "",
    "P006709-2021",
    "",
    null,
    1729593292820
  ),
  new OTSKitManagementListResponse(
    117,
    "United States",
    "P008404-002",
    "P007785-2020",
    "",
    null,
    null,
    null,
    1729593292820
  )
];

let otsKitListResponse = new OTSKitManagementPageResponse(pageable, 5, false, 2, 10, true, sort, 10, 0, false, otsKitList)

let otsKitDetailResponse = {
  "id": 116,
  "description": null,
  "revVersion": "AB",
  "otsKitPartNumberCode": "P008383-002",
  "country": "India",
  "modifiedDate": 1729593292820,
  "probes": [{
    "probePartNumberCode": "P006709-2021",
    "probeConfigGroups": [{
      probeConfigGroupId: 12,
      probeConfigGroupPartNumberCode: "12"
    }]
  }, {
    "probePartNumberCode": "P007785-2021",
    "probeConfigGroups": []
  }]
}

// Common setup function for authentication and permissions
function setupAuthAndPermissions(authSpy: jasmine.SpyObj<AuthJwtService>, permissionSpy: jasmine.SpyObj<PermissionService>) {
  authSpy.isAuthenticate.and.returnValue(true);
  permissionSpy.getKitManagementPermission.and.returnValue(true);
  permissionSpy.getLanguagePermission?.and.returnValue(true);
  permissionSpy.getProbeConfigGroupPermission?.and.returnValue(true);
}

// Common setup for API responses
function setupApiResponses(apiServiceSpy: jasmine.SpyObj<OtsKitManagemantApiCallService>,
  countryCacheSpy: jasmine.SpyObj<CountryCacheService>) {
  apiServiceSpy.getOTSKitList.and.returnValue(
    of(new HttpResponse<OTSKitManagementPageResponse>({
      body: otsKitListResponse,
      status: 200,
      statusText: 'OK',
    }))
  );

  apiServiceSpy.getOTSKitDetail.and.returnValue(
    of(new HttpResponse<OTSKitManagementDetailResponse>({
      body: otsKitDetailResponse,
      status: 200,
      statusText: 'OK',
    }))
  );

  countryCacheSpy.getCountryListFromCache.and.returnValue(
    Promise.resolve(countryListResponse)
  );
}

// Common component initialization
async function initializeComponent(component: OtsKitManagementListComponent, fixture: ComponentFixture<OtsKitManagementListComponent>) {
  component.ngOnInit();
  fixture.detectChanges();
  await fixture.whenStable();
}

describe('OtsKitManagementListComponent', () => {
  let component: OtsKitManagementListComponent;
  let fixture: ComponentFixture<OtsKitManagementListComponent>;
  let exceptionHandlingService: ExceptionHandlingService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let otsKitManagemantApiCallServiceSpy: jasmine.SpyObj<OtsKitManagemantApiCallService>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let countryCacheServiceSpy: jasmine.SpyObj<CountryCacheService>;
  let otsKitManagementServiceSpy: OtsKitManagemantService;

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getKitManagementPermission', 'getLanguagePermission', 'getProbeConfigGroupPermission']);
    countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheServiceSpy', ['getCountryListFromCache']);
    otsKitManagemantApiCallServiceSpy = jasmine.createSpyObj('KitManagemantApiCallService', ['getOTSKitList', 'getOtsKitRevVersion', 'getOTSKitDetail']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    await TestBed.configureTestingModule({
      declarations: [OtsKitManagementListComponent, OtsKitManagementFilterComponent, OtsKitManagementDetailComponent, ProbeConfigGroupDetailComponent, EnumMappingDisplayNamePipe],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      imports: [NgbPagination, ReactiveFormsModule, FormsModule, NgMultiSelectDropDownModule.forRoot()],
      providers: [
        AuthJwtService,
        SessionStorageService,
        HidePermissionNamePipe,
        PrintListPipe,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: OtsKitManagemantApiCallService, useValue: otsKitManagemantApiCallServiceSpy },
        { provide: AuthJwtService, useValue: authServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(OtsKitManagementListComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    otsKitManagementServiceSpy = TestBed.inject(OtsKitManagemantService);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {


    it('should initialize the component when the user is authenticated', async () => {

      authServiceSpy.isAuthenticate.and.returnValue(true);
      permissionServiceSpy.getKitManagementPermission?.and.returnValue(true);
      // Initialize component and trigger change detection
      component.ngOnInit();
      fixture.detectChanges();

      testAuthentication(authServiceSpy, component, fixture);

      //operations List
      operationsListTestcase(fixture, '#otsKitOperation', 1)

      // Test dropdown interaction
      testDropdownInteraction(fixture, component, '#otsKitManagemantListShowEntry');

      // Test pagination
      testPagination(fixture, component, '#otsKit-pagination', 2);

    });
    // Test: Toggles filter visibility and updates the button text
    it('should toggle filter visibility and update the button text', () => {
      // Use the generic utility to test the toggle behavior
      testToggleFilter(component);
    });

    // Test: Initializes the component when authenticated
    it('should initialize component if authenticated', async () => {
      // Arrange: Mock authentication, permissions, and API calls
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      permissionServiceSpy.getKitManagementPermission?.and.returnValue(true);
      otsKitManagemantApiCallServiceSpy.getOtsKitRevVersion?.and.returnValue(
        Promise.resolve('1.0.0') // Mock version response
      );

      // Act: Trigger ngOnInit and wait for asynchronous operations to complete
      component.ngOnInit();
      fixture.detectChanges();
      await fixture.whenStable();

      // Assert: Verify data initialization
      expect(component.dataSizes.length).toBeGreaterThan(0);
      expect(component.otsKitRevVersionResponse).toEqual('1.0.0');

      // Spy on the clickOnRefreshButton method
      spyOn(component, 'clickOnRefreshButton')?.and.callThrough();

      // Check if the refresh button exists and trigger its click event
      const button = fixture.nativeElement.querySelector('#refresh_otsKitList');
      expect(button).toBeTruthy();
      if (button) {
        button.click();
      }

      // Assert: Verify that the clickOnRefreshButton method was called
      expect(component.clickOnRefreshButton).toHaveBeenCalled();
    });


    // Test: Displays an error message when an internal server error occurs
    it('should call toastrService.error with INTERNAL_SERVER_ERROR', () => {
      // Arrange: Mock authentication and simulate a server error
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
      otsKitManagemantApiCallServiceSpy.getOTSKitList?.and.returnValue(throwError(() => mockError));
      spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

      // Act: Trigger the component method that handles API calls
      component.loadAll(null);

      // Assert: Verify that error handling methods were called
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    });

    // Test: Handles error response when loading data
    it('LoadAll method gives error', () => {
      // Arrange: Mock authentication and simulate an invalid API response
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      otsKitManagemantApiCallServiceSpy.getOTSKitList?.and.returnValue(of(new HttpResponse<OTSKitManagementPageResponse>({
        body: null,
        status: 500, // Server error
        statusText: 'OK',
      })));

      // Act: Call the method to load data
      component.loadAll(null);

      // Assert: Verify that no data is loaded and error state is handled
      expect(component.otsKitManagementListResponse.length).toEqual(0);
      expect(component.totalRecordDisplay).toEqual(0);
      expect(component.totalRecord).toEqual(0);
      expect(component.loading).toBeFalse(); // Ensure loading indicator is off
    });
  });

  it('should initialize form controls on ngOnInit', async () => {
    setupAuthAndPermissions(authServiceSpy, permissionServiceSpy);
    setupApiResponses(otsKitManagemantApiCallServiceSpy, countryCacheServiceSpy);

    spyOn(otsKitManagementServiceSpy, 'callOTSKitListFilterRequestParameterSubject').and.callThrough();
    spyOn(component, 'loadAll').and.callThrough();

    await initializeComponent(component, fixture);

    // Access the child filter component instance
    const filterComponent = fixture.debugElement.query(By.directive(OtsKitManagementFilterComponent)).componentInstance;

    // **Assert: Validate the state of the filter component**
    // Ensure the child filter component is rendered and initialized properly
    expect(filterComponent).toBeTruthy();

    // Check that the form controls in the filter component are initialized with default values
    expect(filterComponent.filterOtsKitForm?.get('kitPartNumber').value).toBeNull();
    expect(filterComponent.filterOtsKitForm?.get('probePartNumber').value).toBeNull();
    expect(filterComponent.filterOtsKitForm?.get('countries').value).toEqual([]);

    // **Additional Assertions: Validate component's internal state**
    // Verify the component's permissions and pagination-related properties
    expect(component.bridgeKitDisplayPermissions).toBeTrue(); // Bridge kit display permission should be enabled
    expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE); // Items per page should match the constant value
    expect(component.drpselectsize).toBe(ITEMS_PER_PAGE); // Dropdown size should match the constant value
    expect(component.previousPage).toBe(1); // Default page is the first page
    expect(component.dataSizes).toEqual(['10', '25', '50', '100']); // Dropdown for available page sizes

    // Verify the service method was called to initialize filter parameters
    expect(otsKitManagementServiceSpy.callOTSKitListFilterRequestParameterSubject).toHaveBeenCalled();

    // Assert the component's state reflects the API response
    expect(component.totalItems).toEqual(otsKitListResponse.totalElements); // Total items should match the response
    expect(component.otsKitManagementListResponse).toEqual(otsKitListResponse.content); // Response content should populate the list
    expect(component.totalRecord).toEqual(otsKitListResponse.totalElements); // Total records should match the response
    expect(component.totalRecordDisplay).toEqual(otsKitListResponse.numberOfElements); // Total displayed records should match the response
  });

  it("select All checkbox", async () => {
    // **Arrange: Setup the required dependencies and initial state**
    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Simulate user permissions for Kit Management
    permissionServiceSpy.getKitManagementPermission?.and.returnValue(true);

    // Mock API call to fetch OTS Kit List and return a successful response
    otsKitManagemantApiCallServiceSpy.getOTSKitList?.and.returnValue(
      of(new HttpResponse<OTSKitManagementPageResponse>({
        body: otsKitListResponse,
        status: 200,
        statusText: 'OK',
      }))
    );

    // Spy on the `loadAll` and `selectAllItem` methods of the component to validate their invocation
    spyOn(component, 'loadAll')?.and.callThrough();
    spyOn(component, 'selectAllItem')?.and.callThrough();

    // Set up the component's initial state
    component.isCheckBoxDiaply = true; // Enable the checkbox display
    component.selectAllCheckboxId = 'selectAllOtsKit'; // Assign the checkbox element ID

    // Initialize the component (triggers ngOnInit)
    component.ngOnInit();
    fixture.detectChanges(); // Trigger initial change detection

    // Wait for any pending asynchronous operations to stabilize
    await fixture.whenStable();

    // **Act: Simulate user interaction**
    // Find the "Select All" checkbox element in the DOM
    const selectElementDetails = fixture.nativeElement.querySelector('#selectAllOtsKit');
    selectElementDetails?.click(); // Simulate a click on the "Select All" checkbox

    // **Assert: Verify the expected behavior**
    // Confirm that the `selectAllItem` method is called with the correct argument
    expect(component.selectAllItem).toHaveBeenCalledWith(true);

    // Verify that the total number of selected records matches the total record count
    expect(component.totalRecord).toEqual(component.selectedKitIdList.length);
  });

  it("select one checkbox", async () => {
    setupAuthAndPermissions(authServiceSpy, permissionServiceSpy);
    setupApiResponses(otsKitManagemantApiCallServiceSpy, countryCacheServiceSpy);

    spyOn(component, 'loadAll').and.callThrough();
    spyOn(component, 'selectCheckbox').and.callThrough();

    component.chkPreFix = 'kit';
    component.isCheckBoxDiaply = true;

    await initializeComponent(component, fixture);

    // **Act: Simulate user interactions and verify expected behaviors**
    // Simulate a click on a specific checkbox
    const selectElement = fixture.nativeElement.querySelector('#kit116kit');
    selectElement?.click(); // First click to select the checkbox

    // **Assert: Verify the initial checkbox selection**
    expect(component.selectCheckbox).toHaveBeenCalled(); // Validate method call
    expect(component.selectedKitIdList.length).toEqual(1); // Confirm one item is selected

    // Act: Simulate another click to deselect the checkbox
    selectElement?.click();

    // Assert: Verify the checkbox is deselected
    expect(component.selectedKitIdList.length).toEqual(0); // No items should remain selected

    // **Verify interactions with child components**
    // Access the OTS Kit Management Filter component
    const kitfilterDebugElement = fixture.debugElement.query(By.css('app-ots-kit-management-filter'));
    const kitfilterComponent = kitfilterDebugElement.componentInstance;

    // Assert: Ensure the filter component is rendered
    expect(kitfilterComponent).toBeTruthy();

    // Simulate a filter search without selecting criteria
    const searchFilterButton = fixture.nativeElement.querySelector('#otsKitFilterSearch');
    searchFilterButton?.click(); // Trigger search filter action
    await fixture.whenStable();
    fixture.detectChanges();

    // Assert: Confirm appropriate info message is displayed
    expect(toastrServiceMock.info).toHaveBeenCalledWith("Please Select Filter To Search");

    // Simulate navigation to the detail view
    const selectElementDetails = fixture.nativeElement.querySelector('#otsKitListToDeatil');
    selectElementDetails.click();

    // Assert: Verify display transitions between list and detail views
    expect(component.otsWorldListDisplay).toBeFalsy();
    expect(component.otsWorldDetailDisplay).toBeTruthy();

    fixture.detectChanges();
    await fixture.whenStable();

    // Access the OTS Kit Management Detail component
    const kitDetailDebugElement = fixture.debugElement.query(By.css('app-ots-kit-management-detail'));
    const otsKitDetailComponent = kitDetailDebugElement.componentInstance;

    // Assert: Verify detail component state
    expect(otsKitDetailComponent.otsKitManagementDetailResponse).toEqual(otsKitDetailResponse);
    expect(kitDetailDebugElement).toBeTruthy();

    // Simulate refresh action on the detail component
    spyOn(otsKitDetailComponent, 'refreshOtskitDetailPage').and.callThrough();
    const otsDetailRefreshButton = fixture.nativeElement.querySelector('#otsDetailRefresh');
    otsDetailRefreshButton.click();

    // Assert: Ensure the refresh method is called
    expect(otsKitDetailComponent.refreshOtskitDetailPage).toHaveBeenCalled();

    fixture.detectChanges();
    await fixture.whenStable();

    // Simulate navigation to the PCG (Probe Config Group) detail page
    const otsDetailToPcg = fixture.nativeElement.querySelector('#otsDetailToPcg');
    otsDetailToPcg.click();

    // Assert: Verify display transitions between detail and PCG views
    expect(otsKitDetailComponent.featureDetailPageDisplay).toBeTruthy();
    expect(otsKitDetailComponent.otsDetailPageDisplay).toBeFalsy();

    fixture.detectChanges();
    await fixture.whenStable();

    // Access the Probe Config Group Detail component
    const pcgDetailDebugElement = fixture.debugElement.query(By.css('app-probe-config-group-detail'));
    const pcgKitDetailComponent = pcgDetailDebugElement.componentInstance;

    // Assert: Ensure PCG detail component is rendered
    expect(pcgKitDetailComponent).toBeTruthy();

    // Simulate navigation back to the OTS Detail page
    const pcgDetailsToBackButton = fixture.nativeElement.querySelector('#pcgDetailBack');
    pcgDetailsToBackButton.click();

    // Assert: Verify state transitions back to the detail view
    expect(otsKitDetailComponent.featureDetailPageDisplay).toBeFalsy();
    expect(otsKitDetailComponent.otsDetailPageDisplay).toBeTruthy();

    fixture.detectChanges();
    await fixture.whenStable();

    // Simulate navigation back to the list view
    component.isFilterHidden = true;
    const bridgeDetailBackButton = fixture.nativeElement.querySelector('#otsDetailBack');
    bridgeDetailBackButton.click();

    // Assert: Verify list view is displayed and filters are reset
    expect(component.listPageRefreshForbackToDetailPage).toBeTruthy();
    expect(component.otsWorldListDisplay).toBeTruthy();
    expect(component.isFilterComponentInitWithApicall).toBeFalsy();
    expect(component.otsWorldDetailDisplay).toBeFalsy();
    expect(component.selectedKitIdList.length).toEqual(0);

    // Verify the `showBridgeWorldListDisplay` method is invoked
    component.showBridgeWorldListDisplay();
  });

});
