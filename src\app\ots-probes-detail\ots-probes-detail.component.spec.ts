import { CommonModule, DatePipe } from '@angular/common';
import { HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { INTERNAL_SERVER_ERROR, PROBE_ALREADY_EDIT_ENABLE, PROBE_ALREADY_LOCKED, PROBE_ALREADY_UNLOCKED, PROBE_SERIAL_NUMBER_NOT_EMPTY, PROBE_STATUS_ENABLE, PROBE_DELETE, FeatureHistoryDetailHeader, ProbDetailResource, SALES_ORDER_PARTIALLY_CONFIGURED } from '../app.constants';
import { ConfirmDialogService } from '../confirmationdialog/confirmation.service';
import { AssignFeaturesComponent } from '../FeatureModule/Probe/assign-features/assign-features.component';
import { BasicModelConfig } from '../model/common/BasicModelConfig.model';
import { Pageable } from '../model/common/pageable.model';
import { Sort } from '../model/common/sort.model';
import { SuccessMessageResponse } from '../model/common/SuccessMessageResponse.model';
import { CustomerAssociationRequest } from '../model/customer-association-request';
import { DeviceListByProbeIdPagableResponse } from '../model/probe/DeviceListByProbeIdPagableResponse.model';
import { FeaturePartNumberResponse } from '../model/probe/FeaturePartNumberResponse.model';
import { LicensesRequest } from '../model/probe/multiProbe/LicensesRequest.model';
import { PresetPartNumberResponse } from '../model/probe/PresetPartNumberResponse.model';
import { ProbeDetailWithConfig } from '../model/probe/ProbeDetailWithConfig.model';
import { ProbeDeviceList } from '../model/probe/probeDeviceList.model';
import { ProbeFeatureHistoryListResponse } from '../model/probe/ProbeFeatureHistoryListResponse.model';
import { ProbeHistoryPagableResponse } from '../model/probe/ProbeHistoryPagableResponse.model';
import { ProbeHistoryResponse } from '../model/probe/ProbeHistoryResponse.model';
import { BasicSalesOrderDetailResponse } from '../model/SalesOrder/BasicSalesOrderDetailResponse.model';
import { AuthJwtService } from '../shared/auth-jwt.service';
import { ProductStatusEnum } from '../shared/enum/Common/ProductStatus.enum';
import { ProductConfigStatus } from '../shared/enum/SalesOrder/ProductConfigStatus.enum';
import { ValidityEnum } from '../shared/enum/ValidityEnum.enum';
import { ExceptionHandlingService } from '../shared/ExceptionHandling.service';
import { CustomerAssociationService } from '../shared/modalservice/customer-association.service';
import { FeatureHistoryDetailService } from '../shared/modalservice/feature-history-detail.service';
import { UpdateAssociationService } from '../shared/update-association.service';
import { ProbeService } from '../shared/Service/ProbeService/probe.service';
import { PermissionService } from '../shared/permission.service';
import { CommonBooleanValueDisplayPipe } from '../shared/pipes/common-boolean-value-display.pipe';
import { BooleanKeyValueMappingDisplayNamePipe } from '../shared/pipes/Common/BooleanKeyValueMappingDisplayNamePipe.pipe';
import { EnumMappingDisplayNamePipe } from '../shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from '../shared/pipes/printList.pipe';
import { AssignConfigCheckBoxPipe } from '../shared/pipes/Probe/assign-config-checkbox.pipe';
import { AssignConfigDisablePipe } from '../shared/pipes/Probe/assign-config-disable.pipe';
import { ConfigBaseResponseDisplayPipe } from '../shared/pipes/Probe/config-base-response-display.pipe';
import { FeatureValidityOptionHideShowPipe } from '../shared/pipes/Probe/feature-validity-option-hide-show.pipe';
import { FeaturesBaseResponseDisplayPipe } from '../shared/pipes/Probe/features-base-response-display.pipe';
import { FeaturesCustomEndDateDisplayPipe } from '../shared/pipes/Probe/features-customEndDateDisplay.pipe';
import { FeaturesExpireDateDisplayPipe } from '../shared/pipes/Probe/features-expire-datedisplay.pipe';
import { FeaturesRadioButtonPipe } from '../shared/pipes/Probe/features-radio-button.pipe';
import { FeaturesStartEndDateDisplay } from '../shared/pipes/Probe/features-start-end-dateDisplay.pipe';
import { HidePermissionNamePipe } from '../shared/pipes/Role/hidePermissionName.pipe'; // Ensure correct import path
import { CountryCacheService } from '../shared/Service/CacheService/countrycache.service';
import { PresetApiService } from '../shared/Service/PresetService/preset-api.service';
import { ProbeApiService } from '../shared/Service/ProbeService/probe-api.service';
import { RoleApiCallService } from '../shared/Service/RoleService/role-api-call.service';
import { SalesOrderApiCallService } from '../shared/Service/SalesOrderService/sales-order-api-call.service';
import { SSOLoginService } from '../shared/Service/SSO/ssologin.service';
import { CommonOperationsService } from '../shared/util/common-operations.service';
import { CommonsService } from '../shared/util/commons.service';
import { KeyValueMappingServiceService } from '../shared/util/key-value-mapping-service.service';
import { ModuleValidationServiceService } from '../shared/util/module-validation-service.service';
import { DownloadService } from '../shared/util/download.service';
import { clickOnButtonOrCheckBox, commonsProviders, conformDialog, countryListResponse, featuresListResponse, mockError, presetListResponse, presetResponse, probeTypeResponse, selectOperationOption } from '../Tesing-Helper/test-utils';
import { OtsProbesDetailComponent } from './ots-probes-detail.component';
import { TransferOrderModuleComponent } from '../FeatureModule/TransferOrder/transfer-order-module/transfer-order-module.component';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { FeaturesValidityPartNumberDisplayPipe } from '../shared/pipes/Probe/features-validity-partNumber-Display.pipe';

describe('OtsProbesDetailComponent', () => {
  let component: OtsProbesDetailComponent;
  let fixture: ComponentFixture<OtsProbesDetailComponent>;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let probeApiServiceSpy: jasmine.SpyObj<ProbeApiService>;
  let salesOrderApiCallServiceSpy: jasmine.SpyObj<SalesOrderApiCallService>;
  let countryCacheServiceSpy: jasmine.SpyObj<CountryCacheService>;
  let presetApiServiceSpy: jasmine.SpyObj<PresetApiService>;
  let customerAssociationServicespy: jasmine.SpyObj<CustomerAssociationService>;
  let confirmDialogServiceMock: jasmine.SpyObj<ConfirmDialogService>;

  function createProbeDetailWithConfig({ serialNumber = "ENGDM1.8", locked = false, editable = true, productStatus = ProductStatusEnum.RMA } = {}) {
    return new ProbeDetailWithConfig(
      4211, // id
      "Torso1, USB", // type
      serialNumber, // serialNumber
      "PART1234567890", // partNumber
      "1.0.23", // probeVersion
      1740378882671, // lastConnectedTime
      1740378849244, // licenseDate
      false, // reminder
      "system", // licenseModifiedBy
      null, // countryId
      null, // customerName
      null, // customerEmail
      null, // salesOrderNumber
      1740378883139, // modifiedDate
      "Algeria", // country
      locked, // locked (dynamic value)
      editable, // editable (dynamic value)
      null, // poNumber
      false, // deviceAutoLock
      false, // probeAutoLock
      [
        new LicensesRequest(1, "Trio 2.0", 1740378849244, -1, false, true),
        new LicensesRequest(2, "Auto Preset", 1740378849244, -1, false, true),
        new LicensesRequest(3, "Auto Doppler", 1740378849244, -1, false, true),
        new LicensesRequest(4, "CW Doppler", 1740378849244, -1, false, true),
        new LicensesRequest(5, "AI Fast", 1740378849244, -1, false, true),
        new LicensesRequest(6, "Trio 2.0 (Educational)", 1740378849244, -1, false, true),
        new LicensesRequest(7, "Auto EF", 1740378849244, -1, false, true),
        new LicensesRequest(8, "TDI", 1740378849244, -1, false, true),
        new LicensesRequest(9, "PW Doppler", 1740378849244, -1, false, true)
      ].map((license, index) => {
        const partNumberCodes = ["P008432", "P008332", "P008331", "P007791", "P007790", "P007789", "P007788", "P007787", "P007786"];
        license.partNumberCode = partNumberCodes[index];
        return license;
      }),
      [
        new LicensesRequest(1, "Gyn", 1740378849244, -1, false, true),
        new LicensesRequest(2, "Ob", 1740378849244, -1, false, true),
        new LicensesRequest(3, "Bladder", 1740378849244, -1, false, true),
        new LicensesRequest(4, "Abdomen", 1740378849244, -1, false, true),
        new LicensesRequest(5, "Lungs Torso", 1740378849244, -1, false, true),
        new LicensesRequest(6, "Heart", 1740378849244, -1, false, true),
        new LicensesRequest(7, "Lungs Lexsa", null, null, false, false),
        new LicensesRequest(8, "Msk", null, null, false, false),
        new LicensesRequest(9, "Nerve", null, null, false, false),
        new LicensesRequest(10, "Vascular", null, null, false, false)
      ].map((preset, index) => {
        const partNumberCodes = ["P008491", "P008490", "P008423", "P008422", "P008421", "P008420", null, null, null, null];
        preset.partNumberCode = partNumberCodes[index];
        preset.displayName = preset.name;
        return preset;
      }),
      productStatus, // dynamic productStatus\
      "Transfer Order", // orderRecordType

    );
  }

  const probeHistoryData: Array<ProbeHistoryResponse> = [
    new ProbeHistoryResponse(
      15784,
      1740041739079,
      "akshay.dobariya",
      ["CW Doppler", "Auto EF", "Trio 2.0 (Educational)", "Auto Doppler", "TDI", "Auto Preset", "Trio 2.0", "PW Doppler", "AI Fast"],
      ["Heart", "Ob", "Lungs Torso", "Bladder", "Gyn", "Abdomen"],
      false
    ),
    new ProbeHistoryResponse(
      15718,
      1739181691370,
      "akshay.dobariya",
      [],
      ["Bladder"],
      false
    ),
    new ProbeHistoryResponse(
      11741,
      1727439679744,
      "kshitija.sharma",
      [],
      [],
      false
    ),
    new ProbeHistoryResponse(
      7190,
      1727439679743,
      "kshitija.sharma",
      ["PW Doppler"],
      [],
      false
    ),
    new ProbeHistoryResponse(
      7095,
      1724742367877,
      "dhara.pandit",
      ["PW Doppler"],
      ["Nerve", "Lungs Lexsa", "Msk", "Vascular"],
      false
    ),
    new ProbeHistoryResponse(
      7092,
      1724741519330,
      "dhara.pandit",
      [],
      [],
      false
    ),
    new ProbeHistoryResponse(
      7089,
      1724662232448,
      "dhara.pandit",
      ["PW Doppler"],
      ["Nerve", "Lungs Lexsa", "Msk", "Vascular"],
      false
    ),
    new ProbeHistoryResponse(
      7045,
      1724655513075,
      "jaspreet.virdi",
      [],
      ["Nerve", "Lungs Lexsa", "Msk", "Vascular"],
      true
    ),
    new ProbeHistoryResponse(
      7003,
      1724220599006,
      "dhara.pandit",
      [],
      [],
      false
    ),
    new ProbeHistoryResponse(
      7000,
      1723807306545,
      "dhara.pandit",
      [],
      ["Msk"],
      false
    )
  ];

  const sort = new Sort(true, false, false); // Unsigned, not sorted, not empty
  // Create the Pageable object
  const pageable = new Pageable(sort, 0, 10, 0, true, false);
  let probeHistoryDataList: ProbeHistoryPagableResponse = new ProbeHistoryPagableResponse(pageable, 5, false, 50, 10, true, sort, 10, 0, false, probeHistoryData);

  const deviceInfoList = [
    new ProbeDeviceList(
      4950,
      "107CF56A-EE4F-4041-B089-FEE35D95E33D",
      "IOS",
      "17.5.1",
      "********",
      "0.0.11",
      "Asia/Kolkata",
      "iPad",
      "Apple",
      "1",
      "1",
      null
    ),
    new ProbeDeviceList(
      5638,
      "AD9FBE1A-665A-47DD-839F-7BBC41CB34F2",
      "IOS",
      "17.5.1",
      "********",
      "0.0.11",
      "Asia/Kolkata",
      "iPad",
      "Apple",
      "1",
      "1",
      null
    ),
    new ProbeDeviceList(
      5643,
      "AD9FBE1A-665A-47DD-839F-7BBC41CB34F2",
      "IOS",
      "17.5.1",
      "********",
      "0.0.11",
      "Asia/Kolkata",
      "iPad",
      "Apple",
      "1",
      "1",
      null
    ),
    new ProbeDeviceList(
      5672,
      "AD9FBE1A-665A-47DD-839F-7BBC41CB34F2",
      "IOS",
      "17.5.1",
      "********",
      "0.0.11",
      "Asia/Kolkata",
      "iPad",
      "Apple",
      "1",
      "1",
      null
    ),
    new ProbeDeviceList(
      5674,
      "AD9FBE1A-665A-47DD-839F-7BBC41CB34F2",
      "IOS",
      "17.5.1",
      "********",
      "0.0.11",
      "Asia/Kolkata",
      "iPad",
      "Apple",
      "1",
      "1",
      null
    ),
    new ProbeDeviceList(
      5678,
      "AD9FBE1A-665A-47DD-839F-7BBC41CB34F2",
      "IOS",
      "17.5.1",
      "********",
      "0.0.11",
      "Asia/Kolkata",
      "iPad",
      "Apple",
      "1",
      "1",
      null
    ),
    new ProbeDeviceList(
      5680,
      "AD9FBE1A-665A-47DD-839F-7BBC41CB34F2",
      "IOS",
      "17.5.1",
      "********",
      "0.0.11",
      "Asia/Kolkata",
      "iPad",
      "Apple",
      "1",
      "1",
      null
    ),
    new ProbeDeviceList(
      5685,
      "E2DB2001-6B66-4368-B9B1-97BC59CF3B97",
      "IOS",
      "17.5.1",
      "********",
      "0.0.11",
      "Asia/Kolkata",
      "iPad",
      "Apple",
      "1",
      "1",
      null
    ),
    new ProbeDeviceList(
      5686,
      "E2DB2001-6B66-4368-B9B1-97BC59CF3B97",
      "IOS",
      "17.5.1",
      "********",
      "0.0.11",
      "Asia/Kolkata",
      "iPad",
      "Apple",
      "1",
      "1",
      null
    ),
    new ProbeDeviceList(
      5687,
      "E2DB2001-6B66-4368-B9B1-97BC59CF3B97",
      "IOS",
      "17.5.1",
      "********",
      "0.0.11",
      "Asia/Kolkata",
      "iPad",
      "Apple",
      "1",
      "1",
      null
    )
  ];

  let deviceInfoListResponse: DeviceListByProbeIdPagableResponse = new DeviceListByProbeIdPagableResponse(pageable, 5, false, 50, 10, true, sort, 10, 0, false, deviceInfoList);


  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['confirm', 'getBasicModelConfigForDisableAction', 'getBasicModelConfigForRMAAction']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getProbPermission']);
    probeApiServiceSpy = jasmine.createSpyObj('ProbeApiService', ['getProbeDetailInfo', 'getProbeHistoryDetail', 'generateCSVFileForProbeHistoricalConnection', 'updateProbeFeatures', 'getAsyncProbeDetailInfo', 'getProbeHistory', 'getDeviceListOfProbeId', 'getDeviceListByProbeId', 'getProbeDetailInfo', 'getProbeHistory', 'getAllProbes', 'getFilterValue', 'getProbeTypesList', 'getFeaturesList', 'getPresetsList', 'getProbeTypesList', 'editEnableDisableProbe', 'updateLockState', 'generateCSVFileForProbe', 'downloadCSVFileForProbe', 'deleteProbes', 'rmaProductStatusForProbe', 'disableProductStatusForProbe', 'getProbePresetsList', 'associationProbeWithSalesOrder', 'getprobeTypeResponseList', 'probeTypeUpdate', 'dowloadSasUriofFeatureLicenseAsync', 'getPresetsList']);
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheServiceSpy', ['getCountryListFromCache']);
    salesOrderApiCallServiceSpy = jasmine.createSpyObj('SalesOrderApiCallService', ['getSalesOrderNumberList', 'getBasicSalesOrderDetails']);
    presetApiServiceSpy = jasmine.createSpyObj('PresetApiService', ['getProbePresetsList']);
    customerAssociationServicespy = jasmine.createSpyObj('CustomerAssociationService', ['openCustomerAssociationPopup']);
    await TestBed.configureTestingModule({
      declarations: [
        OtsProbesDetailComponent,
        TransferOrderModuleComponent,
        HidePermissionNamePipe,
        EnumMappingDisplayNamePipe,
        BooleanKeyValueMappingDisplayNamePipe,
        CommonBooleanValueDisplayPipe,
        ConfigBaseResponseDisplayPipe,
        FeaturesStartEndDateDisplay,
        PrintListPipe,
        AssignFeaturesComponent,
        AssignConfigCheckBoxPipe,
        AssignConfigDisablePipe,
        FeaturesBaseResponseDisplayPipe,
        FeatureValidityOptionHideShowPipe,
        FeaturesRadioButtonPipe,
        FeaturesCustomEndDateDisplayPipe,
        FeaturesExpireDateDisplayPipe,
        FeaturesValidityPartNumberDisplayPipe
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      imports: [NgbPaginationModule, NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule, CommonModule],
      providers: [
        ExceptionHandlingService,
        AuthJwtService,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: ProbeApiService, useValue: probeApiServiceSpy },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: PresetApiService, useValue: presetApiServiceSpy },
        { provide: AuthJwtService, useValue: authServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServiceSpy },
        { provide: CustomerAssociationService, useValue: customerAssociationServicespy },
        SessionStorageService,
        CommonsService,
        SSOLoginService,
        RoleApiCallService,
        CommonOperationsService,
        KeyValueMappingServiceService,
        BooleanKeyValueMappingDisplayNamePipe,
        HidePermissionNamePipe,
        DatePipe,
        PrintListPipe,
        EnumMappingDisplayNamePipe,
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(OtsProbesDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  function getProbeDetailInfo(error: boolean = false, data: any = {}) {
    if (error) {
      probeApiServiceSpy.getProbeDetailInfo?.and.returnValue(throwError(() => mockError))
    } else {
      probeApiServiceSpy.getProbeDetailInfo?.and.returnValue(of(new HttpResponse<ProbeDetailWithConfig>({
        body: createProbeDetailWithConfig(data),
        status: 200,
        statusText: 'OK',
      })));

    }
  }

  function getProbeHistory(error: boolean = false) {
    if (error) {
      probeApiServiceSpy.getProbeHistory?.and.returnValue(throwError(() => mockError))
    } else {
      probeApiServiceSpy.getProbeHistory?.and.returnValue(of(new HttpResponse<ProbeHistoryPagableResponse>({
        body: probeHistoryDataList,
        status: 200,
        statusText: 'OK',
      })));

    }
  }

  function getDeviceListOfProbeId(error: boolean = false) {
    if (error) {
      probeApiServiceSpy.getDeviceListByProbeId?.and.returnValue(throwError(() => mockError))
    } else {
      probeApiServiceSpy.getDeviceListByProbeId?.and.returnValue(of(new HttpResponse<DeviceListByProbeIdPagableResponse>({
        body: deviceInfoListResponse,
        status: 200,
        statusText: 'OK',
      })));

    }
  }

  function mockProbeExportCSV(error: boolean = false) {
    if (error) {
      probeApiServiceSpy.generateCSVFileForProbeHistoricalConnection?.and.returnValue(throwError(() => mockError));
    } else {
      probeApiServiceSpy.generateCSVFileForProbeHistoricalConnection?.and.returnValue(of(new HttpResponse<any>({
        body: {
          "fileName": "Probe__1768.xls"  // Mock generated filename
        },
        status: 200,
        statusText: 'OK',
      })));
    }
  }

  function mockProbeDownloadCSV(error: boolean = false) {
    if (error) {
      probeApiServiceSpy.downloadCSVFileForProbe?.and.returnValue(throwError(() => mockError));
    } else {
      probeApiServiceSpy.downloadCSVFileForProbe?.and.returnValue(of(new HttpResponse<any>({
        body: "ghcxdzgxfchjkjhgfcxhrcgji",  // Mock file content
        status: 200,
        statusText: 'OK',
      })));
    }
  }

  function mockProbeApiService() {
    presetApiServiceSpy.getProbePresetsList?.and.returnValue(Promise.resolve(presetListResponse));
    probeApiServiceSpy.getFeaturesList?.and.returnValue(Promise.resolve(featuresListResponse));
    countryCacheServiceSpy.getCountryListFromCache?.and.returnValue(Promise.resolve(countryListResponse));
    probeApiServiceSpy.getprobeTypeResponseList?.and.returnValue(Promise.resolve(probeTypeResponse));
    confirmDialogServiceMock.confirm.and.returnValue(Promise.resolve(true));
    confirmDialogServiceMock.getBasicModelConfigForDisableAction.and.returnValue(new BasicModelConfig("", "", "", ""));
    confirmDialogServiceMock.getBasicModelConfigForRMAAction.and.returnValue(new BasicModelConfig("", "", "", ""));
    probeApiServiceSpy.getPresetsList?.and.returnValue(Promise.resolve(presetResponse))
    probeApiServiceSpy.getProbeTypesList?.and.returnValue(of(new HttpResponse<any>({
      body: ["Lexsa", "Torso1", "Torso1, USB", "Torso3"],
      status: 200,
      statusText: 'OK',
    })));
  }

  function mockProbeApi(action: any, message: string, error: boolean, status: number = 200) {
    if (error) {
      probeApiServiceSpy[action]?.and.returnValue(throwError(() => mockError));
    } else {
      probeApiServiceSpy[action]?.and.returnValue(of(new HttpResponse<SuccessMessageResponse>({
        body: {
          "message": message
        },
        status: status,
        statusText: 'OK',
      })));
    }
  }

  function mockPermission(country: string = "Algeria") {
    // === Authentication & Country Setup ===
    // Mock valid authentication and device permissions
    authServiceSpy.isAuthenticate?.and.returnValue(true);
    permissionServiceSpy.getProbPermission?.and.returnValue(true);
    // Simulate user with access limited to Japan and Argentina countries
    localStorageServiceMock.retrieve.and.returnValue([{ "country": country }, { "country": "Argentina" }]);

  }

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should handle device edit enable and unlock operations with proper validation', async () => {
    // Mock necessary permissions and functionality for the test
    mockPermission();

    // Mock the CSV export functionality and ensure it's active for testing
    mockProbeExportCSV(true);

    // Mock API calls for getting probe details, history, and device list
    getProbeDetailInfo();
    getProbeHistory();
    getDeviceListOfProbeId();

    probeApiServiceSpy.getProbeHistoryDetail.and.returnValue(of(new HttpResponse<ProbeFeatureHistoryListResponse>({
      body: null,
      status: 200,
      statusText: 'OK',
    })));

    // Mocking the return value of API call for probe detail info
    probeApiServiceSpy.getAsyncProbeDetailInfo.and.returnValue(Promise.resolve(createProbeDetailWithConfig()));

    // Mock customer association popup with a resolved promise
    customerAssociationServicespy.openCustomerAssociationPopup?.and.returnValue(
      Promise.resolve(
        new CustomerAssociationRequest(
          true, true,
          new BasicSalesOrderDetailResponse(null, null, null, null, null, false, false, null)
        )
      )
    );

    // Spy on methods to check if refresh methods are called
    spyOn(component, 'refreshProbeDetailPage').and.callThrough();
    spyOn(component, 'refreshLicenseHistory').and.callThrough();
    spyOn(component, 'refreshConnectionHistory').and.callThrough();

    // Mocking different API calls for probe-related operations such as enable/disable, lock/unlock, delete, etc.
    mockProbeApiService();
    mockProbeApi('editEnableDisableProbe', null, true); // Mock enabling/disabling a probe
    mockProbeApi('updateLockState', null, true); // Mock locking/unlocking a probe
    mockProbeApi('deleteProbes', null, true); // Mock deleting a probe
    mockProbeApi('rmaProductStatusForProbe', null, true); // Mock checking RMA status
    mockProbeApi('disableProductStatusForProbe', null, true); // Mock disabling product status
    mockProbeApi('associationProbeWithSalesOrder', null, true); // Mock associating a probe with a sales order
    mockProbeApi('probeTypeUpdate', null, true); // Mock updating probe type
    mockProbeApi('updateProbeFeatures', null, true); // Mock updating probe features

    // Initialize the component and trigger change detection
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Simulate selecting a probe operation and confirming dialog for association update
    await selectOperationOption(fixture, 10, '#probeOperation');
    await conformDialog(fixture, '#updateAssociationOkBtn');
    // Expect an error to be displayed due to an internal server error
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);

    // Select a probe and attempt an operation (e.g., enabling/disabling)
    await selectOperationOption(fixture, 3, '#probeOperation');
    // Expect a message for already unlocked probe
    expect(toastrServiceMock.info).toHaveBeenCalledWith(PROBE_ALREADY_UNLOCKED);

    // Select another operation (e.g., editing)
    await selectOperationOption(fixture, 4, '#probeOperation');

    // Select another operation and expect a message indicating the probe is already enabled
    await selectOperationOption(fixture, 1, '#probeOperation');
    expect(toastrServiceMock.info).toHaveBeenCalledWith(PROBE_ALREADY_EDIT_ENABLE);

    // Simulate another operation failure due to internal error
    await selectOperationOption(fixture, 2, '#probeOperation');
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);

    // Select yet another operation and expect an error message
    await selectOperationOption(fixture, 5, '#probeOperation');

    // Access the dynamically loaded "Create Country" component instance
    const assignFeatureComponentElement = document.querySelector('app-assign-features');

    if (assignFeatureComponentElement && window['ng']) {
      // Access the Angular component instance and simulate feature update changes
      const assignFeatureComponent = window['ng'].getComponent(assignFeatureComponentElement);

      // Simulate feature update for the probe
      assignFeatureComponent.onChangeFeaturesForUpdate(8, true, 1740738843191, {
        displayname: "PW Doppler", featureId: 1, partNumber: [
          new FeaturePartNumberResponse("P008420-001", ValidityEnum.PERPETUAL, false, true, 91),
          new FeaturePartNumberResponse("P008420-001", ValidityEnum.ONE_YEAR, false, true, 10),
        ]
      });

      // Simulate disabling the feature
      assignFeatureComponent.onChangeFeaturesForUpdate(8, false, 1740738843191, {
        displayname: "PW Doppler", featureId: 1, partNumber: [
          new FeaturePartNumberResponse("P008420-001", ValidityEnum.PERPETUAL, false, true, 91),
          new FeaturePartNumberResponse("P008420-001", ValidityEnum.ONE_YEAR, false, true, 10),
        ]
      });

      // Simulate changes to presets for the update
      assignFeatureComponent.onChangePresetsForUpdate(8, true, 1740738843191, {
        displayname: "PW Doppler", featureId: 1, partNumber: [
          new PresetPartNumberResponse("P007786-001", ValidityEnum.PERPETUAL, false, true, 3),
          new PresetPartNumberResponse("P007786-002", ValidityEnum.ONE_YEAR, false, true, 10),
        ]
      });

      // Simulate disabling the preset
      assignFeatureComponent.onChangePresetsForUpdate(8, false, 1740738843191, {
        displayname: "PW Doppler", featureId: 1, partNumber: [
          new PresetPartNumberResponse("P007786-001", ValidityEnum.PERPETUAL, false, true, 3),
          new PresetPartNumberResponse("P007786-002", ValidityEnum.ONE_YEAR, false, true, 10),
        ]
      });

      // Simulate updating end date for the features and presets
      assignFeatureComponent.onChangeFeaturesEndDateForUpdate(1, 1740740424117, '12 Months', null);
      assignFeatureComponent.onChangePresetsEndDateForUpdate(1, 1740740424117, '12 Months', null);

      // Confirm dialog for license assignment and association update
      conformDialog(fixture, '#assignLicenseOkBtn');
      await new Promise((resolve) => setTimeout(resolve, 3000));
      conformDialog(fixture, '#updateAssociationOkBtn');
      expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    }

    // Simulate various probe operations and confirm error messages for internal server errors
    await selectOperationOption(fixture, 7, '#probeOperation');
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);

    await selectOperationOption(fixture, 10, '#probeOperation');
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);

    await selectOperationOption(fixture, 11, '#probeOperation');
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);

    await selectOperationOption(fixture, 12, '#probeOperation');
    // Confirm the update action for the probe
    await conformDialog(fixture, "#declineProbeUpdateBtn");

    await selectOperationOption(fixture, 13, '#probeOperation');
    await selectOperationOption(fixture, 8, '#probeOperation');

    // Trigger refresh actions and check if the respective methods are called
    clickOnButtonOrCheckBox(fixture, "#otsProbeDetailRefresh");
    expect(component.refreshProbeDetailPage).toHaveBeenCalled();

    clickOnButtonOrCheckBox(fixture, "#refreshLicenseHistory");
    expect(component.refreshLicenseHistory).toHaveBeenCalled();

    clickOnButtonOrCheckBox(fixture, "#refreshConnectionHistory");
    expect(component.refreshConnectionHistory).toHaveBeenCalled();

    clickOnButtonOrCheckBox(fixture, "#openProbeConnectionHistory");

    conformDialog(fixture, "#feature-history-detail");
  });

  it('should handle probe edit, enable, unlock, and related operations with proper validation', async () => {
    // Mock permissions required for the test
    mockPermission();

    // Mock CSV export and download functionality
    mockProbeExportCSV();
    mockProbeDownloadCSV(true);

    // Mock API calls for probe details, history, and associated devices
    getProbeDetailInfo(false, { "locked": true, "editable": true, "productStatus": ProductStatusEnum.RMA });
    getProbeHistory();
    getDeviceListOfProbeId();

    // Mock API response for fetching probe details
    probeApiServiceSpy.getAsyncProbeDetailInfo.and.returnValue(Promise.resolve(createProbeDetailWithConfig()));

    // Mocking customer association popup with a resolved promise
    customerAssociationServicespy.openCustomerAssociationPopup?.and.returnValue(
      Promise.resolve(
        new CustomerAssociationRequest(
          true, true,
          new BasicSalesOrderDetailResponse(null, null, null, null, null, false, false, null)
        )
      )
    );

    // Spy on component methods to verify refresh actions are called
    spyOn(component, 'refreshProbeDetailPage').and.callThrough();
    spyOn(component, 'refreshLicenseHistory').and.callThrough();
    spyOn(component, 'refreshConnectionHistory').and.callThrough();

    // Mock API responses for different probe operations
    mockProbeApiService();
    mockProbeApi('editEnableDisableProbe', "The Selected device is marked as editable. You can now make changes.", false);
    mockProbeApi('updateLockState', null, false);
    mockProbeApi('deleteProbes', "Probe(s) deleted successfully.", false);
    mockProbeApi('rmaProductStatusForProbe', "Probe is successfully marked as RMA.", false);
    mockProbeApi('disableProductStatusForProbe', "Probe is successfully marked as Disable.", false);
    mockProbeApi('associationProbeWithSalesOrder', "Sales Order details are updated successfully.", false);
    mockProbeApi('probeTypeUpdate', "Probe type updated successfully.", false);
    mockProbeApi('updateProbeFeatures', "License updated successfully.", false);

    // Initialize the component and trigger change detection
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Simulate selecting a probe operation and confirming dialog for association update
    await selectOperationOption(fixture, 8, '#probeOperation');
    await conformDialog(fixture, '#updateAssociationOkBtn');

    // Expect success message after deleting a probe
    expect(toastrServiceMock.success).toHaveBeenCalledWith("Probe(s) deleted successfully.");

    // Simulate selecting a probe operation (Enable/Disable)
    await selectOperationOption(fixture, 3, '#probeOperation');

    // Simulate selecting another operation (Edit)
    await selectOperationOption(fixture, 4, '#probeOperation');
    expect(toastrServiceMock.info).toHaveBeenCalledWith(PROBE_ALREADY_LOCKED);

    // Simulate selecting another operation (Enable)
    await selectOperationOption(fixture, 1, '#probeOperation');
    expect(toastrServiceMock.info).toHaveBeenCalledWith(PROBE_ALREADY_EDIT_ENABLE);

    // Simulate operation failure due to internal server error
    await selectOperationOption(fixture, 2, '#probeOperation');

    // Simulate another operation and expect an error message
    await selectOperationOption(fixture, 5, '#probeOperation');

    // Confirm dialog for downloading a file
    conformDialog(fixture, '#isDownload');

    // Confirm dialog for license assignment and association update
    conformDialog(fixture, '#assignLicenseOkBtn');

    // Wait for 3 seconds before proceeding with the next steps
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Confirm association update
    conformDialog(fixture, '#updateAssociationOkBtn');

    // Simulate probe operations and verify success messages
    await selectOperationOption(fixture, 6, '#probeOperation');
    expect(toastrServiceMock.success).toHaveBeenCalledWith("Sales Order details are updated successfully.");

    await selectOperationOption(fixture, 9, '#probeOperation');
    expect(toastrServiceMock.success).toHaveBeenCalledWith("Probe is successfully marked as Disable.");

    await selectOperationOption(fixture, 10, '#probeOperation');
    expect(toastrServiceMock.success).toHaveBeenCalledWith("Probe is successfully marked as RMA.");

    // Select an operation and confirm the update action
    await selectOperationOption(fixture, 11, '#probeOperation');
    await conformDialog(fixture, "#uploadBtn");

    // Simulate another operation and expect an internal server error message
    await selectOperationOption(fixture, 12, '#probeOperation');
    expect(toastrServiceMock.error).toHaveBeenCalledOnceWith(INTERNAL_SERVER_ERROR);
  });

  it('should handle probe history and device list operations with error responses', async () => {

    getProbeDetailInfo(true);
    getProbeHistory(true);
    getDeviceListOfProbeId(true);

    // Initialize the component and trigger change detection
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
  });

  it('should handle probe history and device list operations with proper validation', async () => {
    mockPermission();
    getProbeDetailInfo(false, { serialNumber: null, productStatus: ProductStatusEnum.ENABLED });
    getProbeHistory(false);
    getDeviceListOfProbeId(false);

    probeApiServiceSpy.getProbeHistoryDetail.and.returnValue(throwError(() => mockError));

    // Initialize the component and trigger change detection
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    clickOnButtonOrCheckBox(fixture, "#openProbeConnectionHistory");

    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);

    await fixture.whenStable();

    await selectOperationOption(fixture, 13, '#probeOperation');
    expect(toastrServiceMock.info).toHaveBeenCalledWith(PROBE_SERIAL_NUMBER_NOT_EMPTY);

  });

  it('should handle probe history and device list operations with proper validation', async () => {
    mockPermission();
    getProbeDetailInfo(false, { serialNumber: "demo0987", productStatus: ProductStatusEnum.RMA });

    // Initialize the component and trigger change detection
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    await selectOperationOption(fixture, 13, '#probeOperation');
    expect(toastrServiceMock.info).toHaveBeenCalledWith(PROBE_STATUS_ENABLE);

  });

  it('should handle probe history and device list operations with proper validation', async () => {
    mockPermission();
    getProbeDetailInfo(false, { serialNumber: "demo0987", productStatus: ProductStatusEnum.ENABLED });

    // Initialize the component and trigger change detection
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    await selectOperationOption(fixture, 13, '#probeOperation');

    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.transferOrderSelectionDisaplay).toBeFalsy();
  });

  it('should test pagination with loadPage method', async () => {
    mockPermission();
    getProbeDetailInfo();
    getProbeHistory();
    getDeviceListOfProbeId();

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Spy on the getDeviceListOfProbeId method
    const getDeviceListSpy = spyOn(component, 'getDeviceListOfProbeId' as any).and.callThrough();

    // Set previousPage to a different value to trigger the loadPage logic
    component.previousPage = 1;
    component.loadPage(2);

    expect(getDeviceListSpy).toHaveBeenCalledWith(component.probeId);

    // Test when page is the same as previousPage (should not call getDeviceListOfProbeId)
    getDeviceListSpy.calls.reset();
    component.previousPage = 2;
    component.loadPage(2);

    expect(getDeviceListSpy).not.toHaveBeenCalled();
  });

  it('should test feature history pagination with loadFeatureHistoryPage method', async () => {
    mockPermission();
    getProbeDetailInfo();
    getProbeHistory();
    getDeviceListOfProbeId();

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Spy on the getLicenceHistoryListOfProbeId method
    const getLicenceHistorySpy = spyOn(component, 'getLicenceHistoryListOfProbeId' as any).and.callThrough();

    // Set historyPreviousPage to a different value to trigger the loadFeatureHistoryPage logic
    component.historyPreviousPage = 1;
    component.loadFeatureHistoryPage(2);

    expect(getLicenceHistorySpy).toHaveBeenCalledWith(component.probeId);

    // Test when page is the same as historyPreviousPage (should not call getLicenceHistoryListOfProbeId)
    getLicenceHistorySpy.calls.reset();
    component.historyPreviousPage = 2;
    component.loadFeatureHistoryPage(2);

    expect(getLicenceHistorySpy).not.toHaveBeenCalled();
  });

  it('should test changeDataSize method', async () => {
    mockPermission();
    getProbeDetailInfo();
    getProbeHistory();
    getDeviceListOfProbeId();

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Spy on the getDeviceListOfProbeId method
    spyOn(component, 'getDeviceListOfProbeId' as any).and.callThrough();

    // Create a mock event
    const mockEvent = { target: { value: 25 } };
    component.changeDataSize(mockEvent);

    expect(component.loading).toBeFalsy();
    expect(component.itemsPerPage).toBe(25);
    expect(component['getDeviceListOfProbeId']).toHaveBeenCalledWith(component.probeId);
  });

  it('should test changeFeatureHistoryDataSize method', async () => {
    mockPermission();
    getProbeDetailInfo();
    getProbeHistory();
    getDeviceListOfProbeId();

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Spy on the getLicenceHistoryListOfProbeId method
    spyOn(component, 'getLicenceHistoryListOfProbeId' as any).and.callThrough();

    // Create a mock event
    const mockEvent = { target: { value: 25 } };
    component.changeFeatureHistoryDataSize(mockEvent);

    expect(component.loading).toBeFalsy();
    expect(component.historyItemsPerPage).toBe(25);
    expect(component['getLicenceHistoryListOfProbeId']).toHaveBeenCalledWith(component.probeId);
  });

  it('should test back method', async () => {
    mockPermission();
    getProbeDetailInfo();

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Spy on the emit method of showOtsProbe
    spyOn(component.showOtsProbe, 'emit');

    // Call the back method
    component.back();

    // Verify that emit was called
    expect(component.showOtsProbe.emit).toHaveBeenCalled();
  });

  it('should test ngOnDestroy method', async () => {
    mockPermission();
    getProbeDetailInfo();

    // Create spies for the subscription unsubscribe methods
    const unsubscribeSpy1 = jasmine.createSpy('unsubscribe1');
    const unsubscribeSpy2 = jasmine.createSpy('unsubscribe2');
    const unsubscribeSpy3 = jasmine.createSpy('unsubscribe3');

    // Set up the subscriptions with the spies
    component.subscriptionForisloading = { unsubscribe: unsubscribeSpy1 } as any;
    component.subscriptionForDownloadZipFileProbSubject = { unsubscribe: unsubscribeSpy2 } as any;
    component.subscriptionForCommonloading = { unsubscribe: unsubscribeSpy3 } as any;

    // Call ngOnDestroy
    component.ngOnDestroy();

    // Verify that all unsubscribe methods were called
    expect(unsubscribeSpy1).toHaveBeenCalled();
    expect(unsubscribeSpy2).toHaveBeenCalled();
    expect(unsubscribeSpy3).toHaveBeenCalled();
  });

  it('should test validateUserCountry and validateWithUserInfo methods', async () => {
    mockPermission();
    getProbeDetailInfo();

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Spy on the moduleValidationServiceService methods
    const moduleValidationServiceSpy = TestBed.inject(ModuleValidationServiceService) as jasmine.SpyObj<ModuleValidationServiceService>;
    spyOn(moduleValidationServiceSpy, 'validateWithUserCountryForSingleRecord').and.returnValue(true);
    spyOn(moduleValidationServiceSpy, 'validateWithEditStateForSingleRecord').and.returnValue(true);

    // Call the methods
    const validateUserCountryResult = component['validateUserCountry']();
    const validateWithUserInfoResult = component['validateWithUserInfo']();

    // Verify the results
    expect(validateUserCountryResult).toBeTrue();
    expect(validateWithUserInfoResult).toBeTrue();
    expect(moduleValidationServiceSpy.validateWithUserCountryForSingleRecord).toHaveBeenCalled();
    expect(moduleValidationServiceSpy.validateWithEditStateForSingleRecord).toHaveBeenCalled();
  });

  it('should test transferOrderSelectionToggle method', async () => {
    mockPermission();
    getProbeDetailInfo();

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Spy on getProbeDetailInfo
    const getProbeDetailSpy = spyOn(component, 'getProbeDetailInfo' as any);

    // Call transferOrderSelectionToggle with different parameters
    component.transferOrderSelectionToggle(true, false);

    // Verify the state changes
    expect(component.probeDetailDisplay).toBeTrue();
    expect(component.transferOrderSelectionDisaplay).toBeFalse();
    expect(getProbeDetailSpy).toHaveBeenCalledWith(component.probeId);

    // Reset the spy
    getProbeDetailSpy.calls.reset();

    // Call with different parameters
    component.transferOrderSelectionToggle(false, true);

    // Verify the state changes
    expect(component.probeDetailDisplay).toBeFalse();
    expect(component.transferOrderSelectionDisaplay).toBeTrue();
    expect(getProbeDetailSpy).not.toHaveBeenCalled();
  });

  it('should test subjectInit method and subscription handling', async () => {
    // Create spies for the download service methods
    const downloadServiceSpy = TestBed.inject(DownloadService) as jasmine.SpyObj<DownloadService>;
    const commonOperationsServiceSpy = TestBed.inject(CommonOperationsService) as jasmine.SpyObj<CommonOperationsService>;

    // Create observables for the subscriptions
    const isLoadingSubject = of(true);
    const commonLoadingSubject = of(false);
    const downloadZipFileSubject = of(true);

    // Set up the spies to return the observables
    downloadServiceSpy.getisLoadingSubjectForProbDetailPage = jasmine.createSpy().and.returnValue(isLoadingSubject);
    commonOperationsServiceSpy.getCommonLoadingSubject = jasmine.createSpy().and.returnValue(commonLoadingSubject);
    downloadServiceSpy.getdownloadZipFileForProbDetailPageSubject = jasmine.createSpy().and.returnValue(downloadZipFileSubject);

    // Spy on the downloadProbe method
    spyOn(component, 'downloadProbe');

    // Call subjectInit
    component['subjectInit']();

    // Verify that the subscriptions were set up correctly
    expect(downloadServiceSpy.getisLoadingSubjectForProbDetailPage).toHaveBeenCalled();
    expect(commonOperationsServiceSpy.getCommonLoadingSubject).toHaveBeenCalled();
    expect(downloadServiceSpy.getdownloadZipFileForProbDetailPageSubject).toHaveBeenCalled();

    // Verify that the loading state was updated
    expect(component.loading).toBeFalse();

    // Verify that downloadProbe was called with the correct parameter
    expect(component.downloadProbe).toHaveBeenCalledWith(true);
  });

  it('should handle getProbeDetailInfo with non-200 status', async () => {
    mockPermission();

    // Mock API to return non-200 status
    probeApiServiceSpy.getProbeDetailInfo?.and.returnValue(of(new HttpResponse<ProbeDetailWithConfig>({
      body: null,
      status: 404,
      statusText: 'Not Found',
    })));

    spyOn(component, 'back');

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    expect(toastrServiceMock.info).toHaveBeenCalledWith(PROBE_DELETE);
    expect(component.back).toHaveBeenCalled();
  });

  it('should handle openProbeConnectionHistory method', async () => {
    mockPermission();
    getProbeDetailInfo();

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Create a mock history response
    const mockHistory = probeHistoryData[0];

    // Spy on the featureHistoryDetailService
    const featureHistoryDetailServiceSpy = TestBed.inject(FeatureHistoryDetailService) as jasmine.SpyObj<FeatureHistoryDetailService>;
    spyOn(featureHistoryDetailServiceSpy, 'openFeatureHistoryDetailModel').and.returnValue(Promise.resolve(true));

    // Call the method
    component.openProbeConnectionHistory(mockHistory);

    // Verify the service was called with correct parameters
    expect(featureHistoryDetailServiceSpy.openFeatureHistoryDetailModel).toHaveBeenCalledWith(
      FeatureHistoryDetailHeader, mockHistory, component.probeId
    );
  });

  it('should handle downloadProbe method with false parameter', async () => {
    mockPermission();
    getProbeDetailInfo();

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Call downloadProbe with false
    await component.downloadProbe(false);

    // Verify the API was not called
    expect(probeApiServiceSpy.dowloadSasUriofFeatureLicenseAsync).not.toHaveBeenCalled();

    // Call downloadProbe with true
    await component.downloadProbe(true);

    // Verify the API was called
    expect(probeApiServiceSpy.dowloadSasUriofFeatureLicenseAsync).toHaveBeenCalledWith([component.probeId], ProbDetailResource);
  });

  it('should handle ngOnDestroy with undefined subscriptions', () => {
    // Set subscriptions to undefined
    component.subscriptionForisloading = undefined;
    component.subscriptionForDownloadZipFileProbSubject = undefined;
    component.subscriptionForCommonloading = undefined;

    // Call ngOnDestroy - should not throw any errors
    expect(() => component.ngOnDestroy()).not.toThrow();
  });

  it('should handle validateWithUserInfo returning false', async () => {
    mockPermission();
    getProbeDetailInfo();

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Spy on validateWithUserInfo to return false
    spyOn(component, 'validateWithUserInfo' as any).and.returnValue(false);

    // Spy on the updateFeaturesService
    spyOn(component['updateFeaturesService'], 'openAssignProbeFeatureModel');

    // Call updateProbeFeatures
    component.updateProbeFeatures();

    // Verify that the service was not called
    expect(component['updateFeaturesService'].openAssignProbeFeatureModel).not.toHaveBeenCalled();
  });

  it('should handle deleteProbe with validateWithUserInfo returning false', async () => {
    mockPermission();
    getProbeDetailInfo();

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Spy on validateWithUserInfo to return false
    spyOn(component, 'validateWithUserInfo' as any).and.returnValue(false);

    // Spy on the updateAssociationService
    const updateAssociationServiceSpy = TestBed.inject(UpdateAssociationService) as jasmine.SpyObj<UpdateAssociationService>;
    spyOn(updateAssociationServiceSpy, 'openUpdateAssociationModel');

    // Call deleteProbe
    component.deleteProbe();

    // Verify that the service was not called
    expect(updateAssociationServiceSpy.openUpdateAssociationModel).not.toHaveBeenCalled();
  });

  it('should handle transferProbe with SALES_ORDER_PARTIALLY_CONFIGURED status', async () => {
    mockPermission();
    getProbeDetailInfo(false, {
      serialNumber: "TEST123",
      productStatus: ProductStatusEnum.ENABLED,
      soStatus: ProductConfigStatus.PARTIALLY_CONFIGURED
    });

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Spy on validateWithUserInfo to return true
    spyOn(component, 'validateWithUserInfo' as any).and.returnValue(true);

    // Call transferProbe
    component.transferProbe();

    // Verify the correct info message was shown
    expect(toastrServiceMock.info).toHaveBeenCalledWith(PROBE_STATUS_ENABLE);
  });

  it('should handle enableDisableProbe with validateUserCountry returning false', async () => {
    mockPermission();
    getProbeDetailInfo(false, { editable: false });

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Spy on validateWithUserInfo to return true but validateUserCountry to return false
    spyOn(component, 'validateWithUserInfo' as any).and.returnValue(true);
    spyOn(component, 'validateUserCountry' as any).and.returnValue(false);

    // Spy on probeService
    const probeServiceSpy = TestBed.inject(ProbeService) as jasmine.SpyObj<ProbeService>;
    spyOn(probeServiceSpy, 'probeEditAction');

    // Call enableDisableProbe
    await component['enableDisableProbe'](true);

    // Verify that probeEditAction was not called
    expect(probeServiceSpy.probeEditAction).not.toHaveBeenCalled();
  });

  it('should handle lockUnlock with validateUserCountry returning false', async () => {
    mockPermission();
    getProbeDetailInfo(false, { locked: false });

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Spy on validateWithUserInfo to return true but validateUserCountry to return false
    spyOn(component, 'validateWithUserInfo' as any).and.returnValue(true);
    spyOn(component, 'validateUserCountry' as any).and.returnValue(false);

    // Spy on lockUnlockDeviceApicall
    spyOn(component, 'lockUnlockDeviceApicall' as any);

    // Call lockUnlock
    await component.lockUnlock(true);

    // Verify that lockUnlockDeviceApicall was not called
    expect(component['lockUnlockDeviceApicall']).not.toHaveBeenCalled();
  });

  it('should handle lockUnlockDeviceApicall with false response', async () => {
    mockPermission();
    getProbeDetailInfo();

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Mock updateLockState to return false
    probeApiServiceSpy.updateLockState.and.returnValue(Promise.resolve(false));

    // Spy on getProbeDetailInfo
    spyOn(component, 'getProbeDetailInfo' as any);

    // Call lockUnlockDeviceApicall
    await component['lockUnlockDeviceApicall'](true);

    // Verify that getProbeDetailInfo was not called and loading is false
    expect(component['getProbeDetailInfo']).not.toHaveBeenCalled();
    expect(component.loading).toBeFalse();
  });
});
