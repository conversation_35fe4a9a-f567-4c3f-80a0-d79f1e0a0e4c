import { DatePipe } from '@angular/common';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgbPagination } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { INTERNAL_SERVER_ERROR, ITEMS_PER_PAGE, ProbeSuccessMessage, SALES_ORDER_NOT_SELECTED } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { DeviceDetailComponent } from 'src/app/FeatureModule/Device/device-detail/device-detail.component';
import { AssociatedConfigLicence } from 'src/app/model/associated-config-licence.model';
import { Pageable } from 'src/app/model/common/pageable.model';
import { Sort } from 'src/app/model/common/sort.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { FeaturePartNumberResponse } from 'src/app/model/probe/FeaturePartNumberResponse.model';
import { AddUpdateMultiProbeResponse } from 'src/app/model/probe/multiProbe/AddUpdateMultiProbeResponse.model';
import { LicensesRequest } from 'src/app/model/probe/multiProbe/LicensesRequest.model';
import { MultipleProbeResponse } from 'src/app/model/probe/multiProbe/MultipleProbeResponse.model';
import { PresetPartNumberResponse } from 'src/app/model/probe/PresetPartNumberResponse.model';
import { ProbeFeatureResponse } from 'src/app/model/probe/ProbeFeatureResponse.model';
import { ProbePresetResponse } from 'src/app/model/probe/ProbePresetResponse.model';
import { ProbeTypeResponse } from 'src/app/model/probe/probeTypeResponse.model';
import { SalesOrderBridgeDetailResponse } from 'src/app/model/SalesOrder/SalesOrderBridgeDetailResponse.model';
import { SalesOrderDetailResponse } from 'src/app/model/SalesOrder/SalesOrderDetailResponse.model';
import { SalesOrderPageResponse } from 'src/app/model/SalesOrder/SalesOrderPageResponse.model';
import { SalesOrderProbeDetailResponse } from 'src/app/model/SalesOrder/SalesOrderProbeDetailResponse.model';
import { SalesOrderProduct } from 'src/app/model/SalesOrder/SalesOrderProduct.model';
import { SalesOrderResponse } from 'src/app/model/SalesOrder/SalesOrderResponse.model';
import { SalesOrderSchedulerManualSyncTimeResponse } from 'src/app/model/SalesOrder/SalesOrderSchedulerManuleSyncTimeResponse.model';
import { OtsProbesDetailComponent } from 'src/app/ots-probes-detail/ots-probes-detail.component';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { ProductConfigStatus } from 'src/app/shared/enum/SalesOrder/ProductConfigStatus.enum';
import { SalesOrderStatus } from 'src/app/shared/enum/SalesOrder/SalesOrderStatus.enum';
import { SalesOrderTypeStatus } from 'src/app/shared/enum/SalesOrder/SalesOrderTypeStatus.enum';
import { ValidityEnum } from 'src/app/shared/enum/ValidityEnum.enum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ManualSyncService } from 'src/app/shared/manual-sync.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { BooleanKeyValueMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/BooleanKeyValueMappingDisplayNamePipe.pipe';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { FeatureAndPresetInformationDisplayPipe } from 'src/app/shared/pipes/Common/FeatureAndPresetInformationDisplayPipe.pipe';
import { DeviceTypeNamePipe } from 'src/app/shared/pipes/device-type-name.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { SalesOrderBridgeResetButtonDisplayPipe } from 'src/app/shared/pipes/SalesOrder/SalesOrderBridgeResetButtonDisplayPipe.pipe';
import { SalesOrderBridgeViewDetailButtonDisplayPipe } from 'src/app/shared/pipes/SalesOrder/SalesOrderBridgeViewDetailButtonDisplayPipe.pipe';
import { SalesOrderStatusDisplay } from 'src/app/shared/pipes/SalesOrder/SalesOrderStatusDisplay.pipe';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { SalesOrderService } from 'src/app/shared/Service/SalesOrderService/sales-order.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';
import { commonsProviders, selectAllTestCase, selectOperationOption, testAuthentication, testDropdownInteraction, testPagination, testToggleFilter } from 'src/app/Tesing-Helper/test-utils';
import { SalesOrderDetailComponent } from '../sales-order-detail/sales-order-detail.component';
import { SalesOrderFilterComponent } from '../sales-order-filter/sales-order-filter.component';
import { SalesOrderListComponent } from './sales-order-list.component';
import { ManualSyncComponent } from '../manual-sync/manual-sync.component';

describe('SalesOrderListComponent', () => {
  let component: SalesOrderListComponent;
  let fixture: ComponentFixture<SalesOrderListComponent>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let salesOrderServiceSpy: SalesOrderService;
  let confirmDialogServiceMock: jasmine.SpyObj<ConfirmDialogService>
  let salesOrderApiCallServicespy: jasmine.SpyObj<SalesOrderApiCallService>;
  let exceptionHandlingService: ExceptionHandlingService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let probeApiCallServiceSpy: jasmine.SpyObj<ProbeApiService>;
  let ManualSyncServiceSpy: jasmine.SpyObj<ManualSyncService>;


  const sort = new Sort(true, false, false); // Unsigned, not sorted, not empty
  // Create the Pageable object
  const pageable = new Pageable(sort, 0, 10, 0, true, false);

  const content: Array<SalesOrderResponse> = [
    new SalesOrderResponse(
      "00001333", "Jaspreet-Eic_Tets", null, null, null,
      5497, "Croatia", 1732182432000, SalesOrderStatus.BOOKED, SalesOrderTypeStatus.BRIDGE_WORLD,
      1732182482334, 1732206469943, 1732182480000, "PARTIALLY_CONFIGURED", false, false, null
    ),
    new SalesOrderResponse(
      "00001332", "Jaspreet-Eic_Tets", null, null, "test",
      5495, "Croatia", 1731931948000, SalesOrderStatus.BOOKED, SalesOrderTypeStatus.BRIDGE_WORLD,
      *************, *************, *************, "PARTIALLY_CONFIGURED", false, false, null
    ),
    new SalesOrderResponse(
      "********", "Jaspreet-Eic_Tets", null, null, "P0-HDJJE",
      5489, "Ireland", *************, SalesOrderStatus.BOOKED, SalesOrderTypeStatus.BRIDGE_WORLD,
      *************, *************, *************, "PARTIALLY_CONFIGURED", false, false, null
    ),
    new SalesOrderResponse(
      "********", "Test Account - Japan", "<EMAIL>", null, "PO102924",
      5487, "Japan", *************, SalesOrderStatus.BOOKED, SalesOrderTypeStatus.OTS_WORLD,
      *************, *************, *************, "CONFIGURED", false, false, null
    ),
    new SalesOrderResponse(
      "********", "Test Account - Japan", "<EMAIL>", null, "PO102924",
      5486, "United States", *************, SalesOrderStatus.BOOKED, SalesOrderTypeStatus.OTS_WORLD,
      *************, *************, *************, "CONFIGURED", false, false, null
    ),
    new SalesOrderResponse(
      "********", "Test Account - Japan", null, null, "TESTORDER102424",
      5485, "United States", *************, SalesOrderStatus.BOOKED, SalesOrderTypeStatus.OTS_WORLD,
      *************, *************, *************, "CONFIGURED", false, false, null
    ),
    new SalesOrderResponse(
      "********", "Test Account - United States", null, null, "Validation 10-22-24 - Order Number 4",
      5481, "United States", *************, SalesOrderStatus.BOOKED, SalesOrderTypeStatus.OTS_WORLD,
      *************, *************, *************, "CONFIGURED", false, false, null
    ),
    new SalesOrderResponse(
      "********", "Test Account - United States", null, null, "Validation 10-22-24 - Order Number 3",
      5482, "United States", *************, SalesOrderStatus.BOOKED, SalesOrderTypeStatus.OTS_WORLD,
      *************, *************, *************, "PARTIALLY_CONFIGURED", false, false, null
    ),
    new SalesOrderResponse(
      "********", "Jaspreet-Eic_Tets", null, null, null,
      5480, "United States", *************, SalesOrderStatus.BOOKED, SalesOrderTypeStatus.OTS_WORLD,
      *************, *************, *************, "NOT_CONFIGURED", false, false, null
    ),
    new SalesOrderResponse(
      "********", "Jaspreet-Eic_Tets", null, null, null,
      5478, "United States", *************, SalesOrderStatus.BOOKED, SalesOrderTypeStatus.BRIDGE_WORLD,
      *************, *************, *************, "PARTIALLY_CONFIGURED", false, false, null
    )];

  let salesOrderListResponse: SalesOrderPageResponse = new SalesOrderPageResponse(pageable, 39, false, 383, 10, true, sort, 10, 0, false, content);

  let salesorderDetailResponse: SalesOrderDetailResponse = new SalesOrderDetailResponse("********", "Jaspreet-Eic_Tets", null, 8, "P0-HDJJE", 5489, "Ireland", *************, SalesOrderStatus.BOOKED, SalesOrderTypeStatus.BRIDGE_WORLD,
    0, 1732774064174, *************, "NOT_CONFIGURED",
    new SalesOrderProduct(
      [
        new SalesOrderProbeDetailResponse(9138, "Torso1", "bridge-kit-part-number", "ots-kit-part-number", "P007784-004", "ABC123", 1234, ProductConfigStatus.NOT_CONFIGURED, ["part1", "part2"],
          [
            new AssociatedConfigLicence("AI Fast", ValidityEnum.PERPETUAL)
          ],
          [
            new AssociatedConfigLicence("Heart", ValidityEnum.PERPETUAL),
            new AssociatedConfigLicence("Lungs Torso", ValidityEnum.PERPETUAL)
          ],
          [
            new LicensesRequest(1, "Feature 1", 1732774126041, -1, false, true),
            new LicensesRequest(2, "Feature 2", 1732774126041, -1, false, false)
          ],
          [
            new LicensesRequest(3, "Preset 1", 1732774126041, -1, false, true)
          ],
          null,
          true
        ),

        new SalesOrderProbeDetailResponse(
          9139, "Torso1", null, null, "P007784-004", null, null, ProductConfigStatus.NOT_CONFIGURED, [],
          [new AssociatedConfigLicence("AI Fast", ValidityEnum.PERPETUAL)],
          [
            new AssociatedConfigLicence("Heart", ValidityEnum.PERPETUAL),
            new AssociatedConfigLicence("Lungs Torso", ValidityEnum.PERPETUAL),
            new AssociatedConfigLicence("Abdomen", ValidityEnum.PERPETUAL)
          ],
          [
            new LicensesRequest(2, "", null, null, false, false),
            new LicensesRequest(3, "", null, null, false, false),
            new LicensesRequest(9, "", null, null, false, false),
            new LicensesRequest(8, "", null, null, false, false),
            new LicensesRequest(4, "", null, null, false, false),
            new LicensesRequest(7, "", null, null, false, false),
            new LicensesRequest(6, "", null, null, false, false),
            new LicensesRequest(1, "", null, null, false, false),
            new LicensesRequest(5, "", 1732774126041, -1, true, false)
          ],
          [
            new LicensesRequest(1, "", 1732774126041, -1, true, false),
            new LicensesRequest(7, "", null, null, false, false),
            new LicensesRequest(10, "", null, null, false, false),
            new LicensesRequest(5, "", null, null, false, false),
            new LicensesRequest(6, "", null, null, false, false),
            new LicensesRequest(2, "", 1732774126041, -1, true, false),
            new LicensesRequest(4, "", null, null, false, false),
            new LicensesRequest(8, "", null, null, false, false),
            new LicensesRequest(9, "", null, null, false, false),
            new LicensesRequest(3, "", 1732774126041, -1, true, false)
          ],
          null, false
        )
      ],
      [new SalesOrderBridgeDetailResponse(9136, null, "P006030-179", null, null, ProductConfigStatus.NOT_CONFIGURED, null),
      new SalesOrderBridgeDetailResponse(9136, null, "P006030-179", null, null, ProductConfigStatus.NOT_CONFIGURED, null)
      ]
    ), false, false, null
  );


  let getprobeTypeResponseListResponse: Array<ProbeTypeResponse> = [new ProbeTypeResponse(-1, "Please select probe type", "Please select probe type", null, null, null),
  new ProbeTypeResponse(3, "Torso1, USB", "Lexsa", "L1A",
    [
      new ProbeFeatureResponse(1, "Torso1, USB", "PW Doppler", 1, [
        new FeaturePartNumberResponse("P007935-001", ValidityEnum.PERPETUAL, false, true, 9),
        new FeaturePartNumberResponse("P007935-002", ValidityEnum.ONE_YEAR, false, true, 16)
      ])
    ],
    [
      new ProbePresetResponse(6, "Torso1, USB", "Msk", 1, [
        new PresetPartNumberResponse("P008424-001", ValidityEnum.PERPETUAL, false, true, 98)
      ]),
      new ProbePresetResponse(1, "Torso1, USB", "Nerve", 7, [
        new PresetPartNumberResponse("P008425-001", ValidityEnum.PERPETUAL, false, true, 99)
      ]),
      new ProbePresetResponse(1, "Torso1, USB", "Vascular", 8, [
        new PresetPartNumberResponse("P008426-001", ValidityEnum.PERPETUAL, false, true, 100)
      ]),
      new ProbePresetResponse(1, "Torso1, USB", "Lungs Lexsa", 10, [
        new PresetPartNumberResponse("P008427-001", ValidityEnum.PERPETUAL, false, true, 97)
      ])
    ]
  ),
  new ProbeTypeResponse(4, "Torso1, USB", "Torso1, USB", "T1A",
    [
      new ProbeFeatureResponse(1, "Torso1, USB", "PW Doppler", 1, [
        new FeaturePartNumberResponse("P007786-001", ValidityEnum.PERPETUAL, false, true, 3),
        new FeaturePartNumberResponse("P007786-002", ValidityEnum.ONE_YEAR, false, true, 10)
      ]),
      new ProbeFeatureResponse(2, "Torso1, USB", "CW Doppler", 1, [
        new FeaturePartNumberResponse("P007791-001", ValidityEnum.PERPETUAL, false, true, 4),
        new FeaturePartNumberResponse("P007791-002", ValidityEnum.ONE_YEAR, false, true, 11)
      ]),
      new ProbeFeatureResponse(3, "Torso1, USB", "Auto EF", 1, [
        new FeaturePartNumberResponse("P007788-001", ValidityEnum.PERPETUAL, false, true, 5),
        new FeaturePartNumberResponse("P007788-002", ValidityEnum.ONE_YEAR, false, true, 12)
      ]),
      new ProbeFeatureResponse(4, "Torso1, USB", "TDI", 1, [
        new FeaturePartNumberResponse("P007787-001", ValidityEnum.PERPETUAL, false, true, 6),
        new FeaturePartNumberResponse("P007787-002", ValidityEnum.ONE_YEAR, false, true, 13)
      ]),
      new ProbeFeatureResponse(5, "Torso1, USB", "AI Fast", 1, [
        new FeaturePartNumberResponse("P007790-001", ValidityEnum.PERPETUAL, false, true, 7),
        new FeaturePartNumberResponse("P007790-002", ValidityEnum.ONE_YEAR, false, true, 14)
      ]),
      new ProbeFeatureResponse(6, "Torso1, USB", "Trio 2.0", 1, [
        new FeaturePartNumberResponse("P008432-001", ValidityEnum.PERPETUAL, false, true, 8),
        new FeaturePartNumberResponse("P008432-002", ValidityEnum.ONE_YEAR, false, true, 15)
      ]),
      new ProbeFeatureResponse(7, "Torso1, USB", "Auto Preset", 1, [
        new FeaturePartNumberResponse("P008332-001", ValidityEnum.PERPETUAL, false, true, 17),
        new FeaturePartNumberResponse("P008332-002", ValidityEnum.ONE_YEAR, false, true, 18)
      ]),
      new ProbeFeatureResponse(8, "Torso1, USB", "Auto Doppler", 1, [
        new FeaturePartNumberResponse("P008331-001", ValidityEnum.PERPETUAL, false, true, 19),
        new FeaturePartNumberResponse("P008331-002", ValidityEnum.ONE_YEAR, false, true, 20)
      ])
    ],
    [
      new ProbePresetResponse(1, "Torso1, USB", "Heart", 1, [
        new PresetPartNumberResponse("P008420-001", ValidityEnum.PERPETUAL, false, true, 91)
      ]),
      new ProbePresetResponse(1, "Torso1, USB", "Lungs Torso", 2, [
        new PresetPartNumberResponse("P008421-001", ValidityEnum.PERPETUAL, false, true, 92)
      ]),
      new ProbePresetResponse(1, "Torso1, USB", "Abdomen", 3, [
        new PresetPartNumberResponse("P008422-001", ValidityEnum.PERPETUAL, false, true, 93)
      ]),
      new ProbePresetResponse(1, "Torso1, USB", "Bladder", 4, [
        new PresetPartNumberResponse("P008423-001", ValidityEnum.PERPETUAL, false, true, 94)
      ]),
      new ProbePresetResponse(1, "Torso1, USB", "Ob", 5, [
        new PresetPartNumberResponse("P008490-001", ValidityEnum.PERPETUAL, false, true, 95)
      ]),
      new ProbePresetResponse(1, "Torso1, USB", "Gyn", 9, [
        new PresetPartNumberResponse("P008491-001", ValidityEnum.PERPETUAL, false, true, 96)
      ])
    ]
  ),
  new ProbeTypeResponse(1, "Torso1, USB", "Torso1", "T1B",
    [
      new ProbeFeatureResponse(5, "Torso1, USB", "AI Fast", 1, [
        new FeaturePartNumberResponse("P007651-001", ValidityEnum.PERPETUAL, false, true, 1)
      ])
    ],
    [
      new ProbePresetResponse(1, "Torso1, USB", "Heart", 1, [new PresetPartNumberResponse(null, ValidityEnum.PERPETUAL, true, false, 102)]),
      new ProbePresetResponse(1, "Torso1, USB", "Lungs Torso", 2, [new PresetPartNumberResponse(null, ValidityEnum.PERPETUAL, true, false, 103)]),
      new ProbePresetResponse(1, "Torso1, USB", "Abdomen", 3, [new PresetPartNumberResponse(null, ValidityEnum.PERPETUAL, true, false, 104)])
    ]
  ),
  new ProbeTypeResponse(2, "Torso1, USB", "Torso3", "T3B",
    [
      new ProbeFeatureResponse(5, "Torso1, USB", "AI Fast", 1, [
        new FeaturePartNumberResponse("P007651-002", ValidityEnum.PERPETUAL, false, true, 2)
      ])
    ],
    [
      new ProbePresetResponse(1, "Torso1, USB", "Heart", 1, [
        new PresetPartNumberResponse(null, ValidityEnum.PERPETUAL, true, false, 107)
      ]),
      new ProbePresetResponse(1, "Torso1, USB", "Lungs Torso", 2, [
        new PresetPartNumberResponse(null, ValidityEnum.PERPETUAL, true, false, 108)
      ]),
      new ProbePresetResponse(1, "Torso1, USB", "Abdomen", 3, [
        new PresetPartNumberResponse(null, ValidityEnum.PERPETUAL, true, false, 109)
      ])
    ]
  )
  ]
  let salesOrderDetail = {
    "id": 5489,
    "salesOrderNumber": "********",
    "customerName": "Jaspreet-Eic_Tets",
    "customerEmail": null,
    "modifiedDate": 1732774064174,
    "countryId": 8,
    "country": "Ireland",
    "orderType": "BRIDGE_WORLD",
    "poNumber": "P0-HDJJE",
    "soCreatedDate": *************,
    "lastSyncDate": *************,
    "status": "BOOKED",
    "soStatus": "NOT_CONFIGURED",
    "product": {
      "probes": [
        {
          "sopmId": 9138,
          "probeType": "Torso1",
          "bridgeKitPartNumberCode": null,
          "otsKitPartNumberCode": null,
          "productCode": "P007784-004",
          "entitySerialNumber": null,
          "entityPk": null,
          "entityStatus": "NOT_CONFIGURED",
          "probeConfigGroupPartNumberCodes": [],
          "associatedFeatures": [{
            "displayName": "AI Fast",
            "validity": "PERPETUAL"
          }],
          "associatedPresets": [{
            "displayName": "Heart",
            "validity": "PERPETUAL"
          }, {
            "displayName": "Lungs Torso",
            "validity": "PERPETUAL"
          }, {
            "displayName": "Abdomen",
            "validity": "PERPETUAL"
          }],
          "enableFeatures": [new LicensesRequest(2, "", null, null, false, false),
          new LicensesRequest(3, "", null, null, false, false),
          new LicensesRequest(9, "", null, null, false, false),
          new LicensesRequest(8, "", null, null, false, false),
          new LicensesRequest(4, "", null, null, false, false),
          new LicensesRequest(7, "", null, null, false, false),
          new LicensesRequest(6, "", null, null, false, false),
          new LicensesRequest(1, "", null, null, false, false),
          new LicensesRequest(5, "", 1732796562082, -1, true, false)],
          "enablePresets": [new LicensesRequest(1, "", 1732796562082, -1, true, false),
          new LicensesRequest(7, "", null, null, false, false),
          new LicensesRequest(10, "", null, null, false, false),
          new LicensesRequest(5, "", null, null, false, false),
          new LicensesRequest(6, "", null, null, false, false),
          new LicensesRequest(2, "", 1732796562082, -1, true, false),
          new LicensesRequest(4, "", null, null, false, false),
          new LicensesRequest(8, "", null, null, false, false),
          new LicensesRequest(9, "", null, null, false, false),
          new LicensesRequest(3, "", 1732796562082, -1, true, false)
          ],
          "reminder": false
        }, {
          "sopmId": 9139,
          "probeType": "Torso1",
          "bridgeKitPartNumberCode": null,
          "otsKitPartNumberCode": null,
          "productCode": "P007784-004",
          "entitySerialNumber": null,
          "entityPk": null,
          "entityStatus": "NOT_CONFIGURED",
          "probeConfigGroupPartNumberCodes": [],
          "associatedFeatures": [{
            "displayName": "AI Fast",
            "validity": "PERPETUAL"
          }],
          "associatedPresets": [{
            "displayName": "Heart",
            "validity": "PERPETUAL"
          }, {
            "displayName": "Lungs Torso",
            "validity": "PERPETUAL"
          }, {
            "displayName": "Abdomen",
            "validity": "PERPETUAL"
          }],
          "enableFeatures": [new LicensesRequest(2, "", null, null, false, false),
          new LicensesRequest(3, "", null, null, false, false),
          new LicensesRequest(9, "", null, null, false, false),
          new LicensesRequest(8, "", null, null, false, false),
          new LicensesRequest(4, "", null, null, false, false),
          new LicensesRequest(7, "", null, null, false, false),
          new LicensesRequest(6, "", null, null, false, false),
          new LicensesRequest(1, "", null, null, false, false),
          new LicensesRequest(5, "", 1732796562082, -1, true, false)],
          "enablePresets": [new LicensesRequest(1, "", 1732796562082, -1, true, false),
          new LicensesRequest(7, "", null, null, false, false),
          new LicensesRequest(10, "", null, null, false, false),
          new LicensesRequest(5, "", null, null, false, false),
          new LicensesRequest(6, "", null, null, false, false),
          new LicensesRequest(2, "", 1732796562082, -1, true, false),
          new LicensesRequest(4, "", null, null, false, false),
          new LicensesRequest(8, "", null, null, false, false),
          new LicensesRequest(9, "", null, null, false, false),
          new LicensesRequest(3, "", 1732796562082, -1, true, false)],
          "reminder": false
        }, {
          "sopmId": 9138,
          "probeType": "Torso1",
          "bridgeKitPartNumberCode": null,
          "otsKitPartNumberCode": null,
          "productCode": "P007784-004",
          "entitySerialNumber": "T1B-123-87",
          "entityPk": 4093,
          "entityStatus": "CONFIGURED",
          "probeConfigGroupPartNumberCodes": [],
          "associatedFeatures": [{
            "displayName": "AI Fast",
            "validity": "PERPETUAL"
          }],
          "associatedPresets": [{
            "displayName": "Heart",
            "validity": "PERPETUAL"
          }, {
            "displayName": "Lungs Torso",
            "validity": "PERPETUAL"
          }, {
            "displayName": "Abdomen",
            "validity": "PERPETUAL"
          }],
          "enableFeatures": [new LicensesRequest(2, "", null, null, false, false),
          new LicensesRequest(3, "", null, null, false, false),
          new LicensesRequest(9, "", null, null, false, false),
          new LicensesRequest(8, "", null, null, false, false),
          new LicensesRequest(4, "", null, null, false, false),
          new LicensesRequest(7, "", null, null, false, false),
          new LicensesRequest(6, "", null, null, false, false),
          new LicensesRequest(1, "", null, null, false, false),
          new LicensesRequest(5, "", 1733130704964, -1, true, false)
          ],
          "enablePresets": [new LicensesRequest(1, "", 1733130704964, -1, true, false),
          new LicensesRequest(7, "", null, null, false, false),
          new LicensesRequest(10, "", null, null, false, false),
          new LicensesRequest(5, "", null, null, false, false),
          new LicensesRequest(6, "", null, null, false, false),
          new LicensesRequest(2, "", 1733130704964, -1, true, false),
          new LicensesRequest(4, "", null, null, false, false),
          new LicensesRequest(8, "", null, null, false, false),
          new LicensesRequest(9, "", null, null, false, false),
          new LicensesRequest(3, "", 1733130704964, -1, true, false)
          ],
          "reminder": false
        }],
      "bridges": [{
        "sopmId": 9136,
        "bridgeKitPartNumberCode": null,
        "productCode": "P006030-179",
        "entitySerialNumber": null,
        "entityPk": null,
        "entityStatus": "IN_PROGRESS"
      }, {
        "sopmId": 9137,
        "bridgeKitPartNumberCode": null,
        "productCode": "P006030-179",
        "entitySerialNumber": null,
        "entityPk": 1231,
        "entityStatus": "CONFIGURED"
      }]
    }
  }

  let saveSerialNumberSuccessResponse: AddUpdateMultiProbeResponse = new AddUpdateMultiProbeResponse(false, [new MultipleProbeResponse(0, '', '', 4092, "T1B-PROBE-564", null)])

  let saveSerialNumberErrorResponse: AddUpdateMultiProbeResponse = new AddUpdateMultiProbeResponse(true, [new MultipleProbeResponse(0, '', '', 4093, "T1A-123-45", "Serial number should start with T1B.")])

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['confirm']);
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getSalesOrderPermission', 'getProbPermission', 'getDevicePermission']);
    salesOrderApiCallServicespy = jasmine.createSpyObj('SalesOrderApiCallService', ['getSalesOrderList', 'deleteSalesOrder', 'getSalesOrderSchedulerSyncTime', 'getSalesOrderDetails', 'resetSalesOrderBridge', 'updateBridgeInfo', 'salesOrderManualSync']);
    probeApiCallServiceSpy = jasmine.createSpyObj('ProbeApiService', ['getprobeTypeResponseList', 'saveMutiprobe', 'getProbeDetailInfo', 'getProbeHistory', 'getDeviceListByProbeId']);
    ManualSyncServiceSpy = jasmine.createSpyObj('ManualSyncService', ['confirm']);

    await TestBed.configureTestingModule({
      declarations: [
        SalesOrderListComponent,
        SalesOrderFilterComponent,
        SalesOrderDetailComponent,
        FeatureAndPresetInformationDisplayPipe,
        SalesOrderStatusDisplay,
        SalesOrderBridgeViewDetailButtonDisplayPipe,
        SalesOrderBridgeResetButtonDisplayPipe,
        OtsProbesDetailComponent,
        EnumMappingDisplayNamePipe,
        BooleanKeyValueMappingDisplayNamePipe,
        DeviceDetailComponent,
        DeviceTypeNamePipe,
        ManualSyncComponent
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      imports: [NgbPagination,
        NgMultiSelectDropDownModule.forRoot(),
        ReactiveFormsModule,
        FormsModule],
      providers: [
        SessionStorageService,
        DatePipe,
        HidePermissionNamePipe,
        PrintListPipe,
        EnumMappingDisplayNamePipe,
        BooleanKeyValueMappingDisplayNamePipe,
        { provide: ManualSyncService, useValue: ManualSyncServiceSpy },
        { provide: ProbeApiService, useValue: probeApiCallServiceSpy },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        KeyValueMappingServiceService,
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServicespy },
        { provide: AuthJwtService, useValue: authServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();
    fixture = TestBed.createComponent(SalesOrderListComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    salesOrderServiceSpy = TestBed.inject(SalesOrderService);
    probeApiCallServiceSpy.getprobeTypeResponseList?.and.returnValue(
      Promise.resolve(getprobeTypeResponseListResponse)
    );
    ManualSyncServiceSpy.confirm.and.returnValue(Promise.resolve(true));
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {

    it('should initialize the component when the user is authenticated', async () => {

      // Simulate user authentication
      authServiceSpy.isAuthenticate?.and.returnValue(true);

      // Simulate user permissions for accessing Sales Order functionality
      permissionServiceSpy.getSalesOrderPermission?.and.returnValue(true);

      component.ngOnInit();
      // **Act: Trigger Angular lifecycle methods and finalize change detection**
      fixture.detectChanges(); // Run initial change detection
      await fixture.whenStable(); // Wait for asynchronous operations to complete

      spyOn(component, 'refreshFilter')?.and.callThrough();
      const button = fixture.nativeElement.querySelector('#refresh_salesOrderList');
      expect(button).toBeTruthy();
      button?.click();
      expect(component.refreshFilter).toHaveBeenCalled();

      testAuthentication(authServiceSpy, component, fixture);

      // Test dropdown interaction
      testDropdownInteraction(fixture, component, '#salesOrderListShowEntry');

      // Test pagination
      testPagination(fixture, component, '#salesOrderList-pagination', 2);
    });

    // Test: Toggles filter visibility and updates the button text
    it('should toggle filter visibility and update the button text', () => {
      // Use the generic utility to test the toggle behavior
      testToggleFilter(component);
    });

    // Test: Displays an error message when an internal server error occurs
    it('should display an error message when an internal server error occurs', () => {
      // Arrange: Simulate a 500 Internal Server Error
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
      salesOrderApiCallServicespy.getSalesOrderList?.and.returnValue(throwError(() => mockError));
      spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

      // Act: Trigger the data loading method
      component.loadAll(null);

      // Assert: Verify the error handling logic is triggered
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    });

    // Test: Handles an error response when loading data
    it('should handle error response in the LoadAll method', () => {
      // Arrange: Simulate a 500 status response with no body
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      salesOrderApiCallServicespy.getSalesOrderList?.and.returnValue(of(new HttpResponse<SalesOrderPageResponse>({
        body: null,
        status: 500,
        statusText: 'OK',
      })));

      // Act: Call `loadAll` to attempt data loading
      component.loadAll(null);

      // Assert: Verify no data is loaded and error state is managed
      expect(component.salesOrderResponseList.length).toEqual(0);
      expect(component.totalRecordDisplay).toEqual(0);
      expect(component.totalRecord).toEqual(0);
      expect(component.loading).toBeFalse();
    });
  });

  it('should initialize form controls on ngOnInit sales Order', async () => {
    // **Arrange: Setup the required dependencies and initial state**
    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Simulate user permissions for accessing Sales Order functionality
    permissionServiceSpy.getSalesOrderPermission?.and.returnValue(true);

    // Mock API call to fetch the Sales Order list and return a successful response
    salesOrderApiCallServicespy.getSalesOrderList?.and.returnValue(of(new HttpResponse<SalesOrderPageResponse>({
      body: salesOrderListResponse, // Mocked response data
      status: 200, // Simulate a successful API response
    })));

    // Spy on the relevant service and component methods to track their invocation
    spyOn(salesOrderServiceSpy, 'callSalesOrderListFilterRequestParameterSubject')?.and.callThrough();
    spyOn(component, 'loadAll')?.and.callThrough();

    // Initialize the component (triggers ngOnInit and sets up component state)
    component.ngOnInit();
    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution

    // Access the child filter component instance for validation
    const filterComponent = fixture.debugElement.query(By.directive(SalesOrderFilterComponent)).componentInstance;

    // **Assert: Validate the state of the filter component**
    // Ensure the child filter component is properly rendered and initialized
    expect(filterComponent).toBeTruthy();

    // Verify that the form controls in the filter component are initialized with default values
    expect(filterComponent.filterSalesOrderForm?.get('salesOrderNumber').value).toBe(''); // Default value for `salesOrderNumber`
    expect(filterComponent.filterSalesOrderForm?.get('customerName').value).toBe(''); // Default value for `customerName`
    expect(filterComponent.filterSalesOrderForm?.get('customerEmail').value).toBe(''); // Default value for `customerEmail`
    expect(filterComponent.filterSalesOrderForm?.get('countries').value).toEqual([]); // Default value for `countries`
    expect(filterComponent.filterSalesOrderForm?.get('orderType').value).toEqual([]); // Default value for `orderType`
    expect(filterComponent.filterSalesOrderForm?.get('po').value).toBe(''); // Default value for `po`
    expect(filterComponent.filterSalesOrderForm?.get('productConfigStatus').value).toEqual([]); // Default value for `productConfigStatus`

    // **Act: Trigger Angular lifecycle methods and finalize change detection**
    fixture.detectChanges(); // Run initial change detection
    await fixture.whenStable(); // Wait for asynchronous operations to complete

    // **Additional Assertions: Validate component's internal state**
    // Verify permissions and pagination-related properties
    expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE); // Items per page should match the configured constant
    expect(component.drpselectsize).toBe(ITEMS_PER_PAGE); // Dropdown size should match the configured constant
    expect(component.previousPage).toBe(1); // Default page should be the first page
    expect(component.dataSizes).toEqual(['10', '25', '50', '100']); // Data sizes for pagination dropdown

    // Verify that the service method was called to initialize filter parameters
    expect(salesOrderServiceSpy.callSalesOrderListFilterRequestParameterSubject).toHaveBeenCalled();

    // Assert that the component state reflects the API response data
    expect(component.totalItems).toEqual(salesOrderListResponse.totalElements); // Total items should match the API response
    expect(component.salesOrderResponseList).toEqual(salesOrderListResponse.content); // List should populate with the response content
    expect(component.totalRecord).toEqual(salesOrderListResponse.totalElements); // Total records should match the response
    expect(component.totalRecordDisplay).toEqual(salesOrderListResponse.numberOfElements); // Displayed records should match the response
  });

  it("should handle 'Select All' checkbox functionality in sales order", async () => {
    // **Arrange: Setup the required dependencies and initial state**
    // Mock user authentication and permissions
    authServiceSpy.isAuthenticate?.and.returnValue(true); // Simulate an authenticated user
    permissionServiceSpy.getSalesOrderPermission?.and.returnValue(true); // Simulate permission grant for Sales Order

    // Mock the confirmation dialog to return a positive response
    confirmDialogServiceMock.confirm.and.returnValue(Promise.resolve(true));

    // Mock API call to fetch the Sales Order list and return a successful response
    salesOrderApiCallServicespy.getSalesOrderList?.and.returnValue(of(new HttpResponse<SalesOrderPageResponse>({
      body: salesOrderListResponse, // Mocked response data
      status: 200, // Simulate a successful API response
    })));

    salesOrderApiCallServicespy.salesOrderManualSync?.and.returnValue(of(new HttpResponse<SalesOrderSchedulerManualSyncTimeResponse>({
      body: { id: 12345 }, // Mocked response data
      status: 200, // Simulate a successful API response
    })));

    // Mock API response for deleting a Sales Order
    salesOrderApiCallServicespy.deleteSalesOrder?.and.returnValue(of(new HttpResponse<SuccessMessageResponse>({
      body: { message: 'Sales Order deleted successfully.' }, // Mocked delete success message
      status: 200, // Successful deletion
      statusText: 'OK',
    })));

    // Spy on critical component methods to validate their invocation
    spyOn(component, 'loadAll')?.and.callThrough(); // Track calls to the `loadAll` method

    // Set up the initial state by assigning the checkbox element ID
    component.selectAllCheckboxId = 'selectAllSalesOrder';

    // Initialize the component (triggers ngOnInit)
    component.ngOnInit();
    fixture.detectChanges(); // Trigger initial change detection

    // Wait for asynchronous operations to stabilize
    await fixture.whenStable();

    selectAllTestCase(fixture, component, '#selectAllSalesOrder')

    // Wait for asynchronous operations to stabilize
    await fixture.whenStable();

    // **Act: Simulate selection of an individual checkbox**
    // Find a specific checkbox element and simulate its selection
    const individualCheckbox = fixture.debugElement.query(By.css('#salesOrder5497salesOrder')).nativeElement as HTMLInputElement;
    individualCheckbox.click(); // User selects one item
    fixture.detectChanges();
    individualCheckbox.click(); // User selects one item

    // Wait for asynchronous operations to stabilize
    await fixture.whenStable();

    await selectOperationOption(fixture, 1, '#salesOrderOperation');

    individualCheckbox.click(); // User selects one item
    // **Act: Simulate clicking the "Delete" button**
    // Find the delete button and simulate a click
    await selectOperationOption(fixture, 2, '#salesOrderOperation');

    fixture.detectChanges(); // Trigger change detection to reflect the updated state
    await fixture.whenStable();
    // **Assert: Verify successful deletion message**
    // Ensure the success message is displayed to the user
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Sales Order deleted successfully.');
  });

  it("select one checkbox", async () => {
    // **Arrange: Set up necessary dependencies and initial state**

    // Mock user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);
    permissionServiceSpy.getSalesOrderPermission?.and.returnValue(true);
    confirmDialogServiceMock.confirm.and.returnValue(Promise.resolve(true));

    // Mock API calls for Sales Order List and Detail, returning successful responses
    salesOrderApiCallServicespy.getSalesOrderList?.and.returnValue(of(new HttpResponse<SalesOrderPageResponse>({
      body: salesOrderListResponse,
      status: 200,
      statusText: 'OK',
    })));
    salesOrderApiCallServicespy.getSalesOrderDetails?.and.returnValue(
      of(new HttpResponse<SalesOrderDetailResponse>({
        body: salesorderDetailResponse,
        status: 200,
        statusText: 'OK',
      }))
    );

    salesOrderApiCallServicespy.salesOrderManualSync?.and.returnValue(throwError(() => mockError))

    // Mock API call for delete operation to return an error
    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    salesOrderApiCallServicespy.deleteSalesOrder?.and.returnValue(throwError(() => mockError));

    // Spy on critical component methods to ensure they are called during the test
    spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();
    spyOn(component, 'loadAll')?.and.callThrough();
    spyOn(component, 'selectCheckbox')?.and.callThrough();

    // Set the initial state for the component
    component.chkPreFix = 'salesOrder'; // Prefix for checkbox IDs

    // Trigger component initialization (calls ngOnInit)
    component.ngOnInit();
    // Wait for asynchronous tasks to stabilize
    await fixture.whenStable();
    fixture.detectChanges(); // Trigger change detection

    await selectOperationOption(fixture, 2, '#salesOrderOperation');

    expect(toastrServiceMock.info).toHaveBeenCalledWith(SALES_ORDER_NOT_SELECTED);

    // Arrange: Select the checkbox element by ID
    // Get the checkbox DebugElement
    const checkboxDebugEl = fixture.debugElement.query(By.css('#salesOrder5489salesOrder'));
    // Get the native element
    const checkboxNativeEl = checkboxDebugEl.nativeElement as HTMLInputElement;

    // Simulate the click
    checkboxNativeEl.click();

    // Assert if the checkbox is checked
    expect(checkboxNativeEl.checked).toBeTrue();
    // **Assert: Verify the initial checkbox selection**
    expect(component.selectCheckbox).toHaveBeenCalled(); // Validate method call
    expect(component.selectedSalesOrderIdList.length).toEqual(1); // Confirm one item is selected

    await selectOperationOption(fixture, 2, '#salesOrderOperation');

    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    // Act: Simulate another click to deselect the checkbox
    checkboxNativeEl.click();

    // Assert: Verify the checkbox is deselected
    expect(component.selectedSalesOrderIdList.length).toEqual(0); // No items should remain selected

    await selectOperationOption(fixture, 1, '#salesOrderOperation');

    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);

    // **Verify interactions with child components**
    // Access the Sales Order Filter component
    const salesOrderfilterDebugElement = fixture.debugElement.query(By.css('app-sales-order-filter'));
    const salesOrderfilterComponent = salesOrderfilterDebugElement.componentInstance;

    // Assert: Ensure the filter component is rendered
    expect(salesOrderfilterComponent).toBeTruthy();

    salesOrderfilterComponent.listPageRefreshForbackToDetailPage = true;
    // Simulate a filter search without selecting criteria
    const searchFilterButton = fixture.nativeElement.querySelector('#salesOrderFilterSearch');
    searchFilterButton?.click(); // Trigger search filter action
    await fixture.whenStable();
    fixture.detectChanges();

    // Assert: Confirm appropriate info message is displayed
    expect(toastrServiceMock.info).toHaveBeenCalledWith("Please Select Filter To Search");
    salesOrderfilterComponent.setFilterValue();
    // Simulate navigation to the detail view
    const selectElementDetails = fixture.nativeElement.querySelector('#salesOrderListToDeatil');
    selectElementDetails?.click();

    // Assert: Verify display transitions between list and detail views
    expect(component.salesOrderListDisplay).toBeFalsy();
    expect(component.salesOrderDetailDisplay).toBeTruthy();

    fixture.detectChanges();
    await fixture.whenStable();

    // Access the Sales Order Detail component
    const salesOrderDetailDebugElement = fixture.debugElement.query(By.css('app-sales-order-detail'));
    const salesOrderDetailDetailComponent = salesOrderDetailDebugElement.componentInstance;

    fixture.detectChanges();
    await fixture.whenStable();

    expect(salesOrderDetailDetailComponent).toBeTruthy();

    // Simulate refresh action on the detail component
    spyOn(salesOrderDetailDetailComponent, 'refreshFilter').and.callThrough();
    const salesOrderDetailRefreshButton = fixture.nativeElement.querySelector('#salesOrderRefresh');
    salesOrderDetailRefreshButton?.click();

    // Assert: Ensure the refresh method is called
    expect(salesOrderDetailDetailComponent.refreshFilter).toHaveBeenCalled();

    fixture.detectChanges();
    await fixture.whenStable();

    // Simulate navigation back to the list view
    spyOn(salesOrderDetailDetailComponent, 'deleteSalesOrder').and.callThrough();
    await selectOperationOption(fixture, 1, '#salesOrderDetailOperation');

    expect(salesOrderDetailDetailComponent.deleteSalesOrder).toHaveBeenCalled();

    // Assert: Verify that error handling methods were called
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);

  });

  it("should delete a selected item and verify its removal", async () => {
    // **Arrange: Set up necessary dependencies and initial state**

    // Mock user authentication to simulate a logged-in user
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Simulate user permissions for accessing Sales Order functionality
    permissionServiceSpy.getSalesOrderPermission?.and.returnValue(true);
    permissionServiceSpy.getDevicePermission?.and.returnValue(true);
    permissionServiceSpy.getProbPermission?.and.returnValue(true);

    // Simulate confirmation dialog response as 'true' (user confirms the delete action)
    confirmDialogServiceMock.confirm.and.returnValue(Promise.resolve(true));

    // Mock API call for fetching Sales Order  List with a successful response
    salesOrderApiCallServicespy.getSalesOrderList?.and.returnValue(of(new HttpResponse<SalesOrderPageResponse>({
      body: salesOrderListResponse,
      status: 200,
      statusText: 'OK',
    })));

    // Mock API call for fetching Sales Order Save SerialNumber  with a successful response
    probeApiCallServiceSpy.saveMutiprobe?.and.returnValue(of(new HttpResponse<AddUpdateMultiProbeResponse>({
      body: saveSerialNumberSuccessResponse,
      status: 200,
      statusText: 'OK',
    })));

    // Mock API call for fetching Sales Order  Details with a successful response
    salesOrderApiCallServicespy.getSalesOrderDetails?.and.returnValue(
      of(new HttpResponse<SalesOrderDetailResponse>({
        body: salesorderDetailResponse,
        status: 200,
        statusText: 'OK',
      }))
    );

    // Mock API call for fetching Sales Order  Details with a successful response
    salesOrderApiCallServicespy.resetSalesOrderBridge?.and.returnValue(
      Promise.resolve({
        "sopmId": 9136,
        "productEntityId": 1,
        "productSerialNumber": "123456",
        "productStatus": ProductConfigStatus.NOT_CONFIGURED
      })
    );

    // Mock API call for deleting a Sales Order with a successful response
    salesOrderApiCallServicespy.deleteSalesOrder?.and.returnValue(
      of(new HttpResponse<SuccessMessageResponse>({
        body: { message: 'sales order deleted successfully.' }, // Success message
        status: 200,
        statusText: 'OK',
      }))
    );

    // Spy on the `loadAll` method to ensure it is called during the test
    spyOn(component, 'loadAll')?.and.callThrough();

    // Spy on the `CustomerrorMessage` method of `exceptionHandlingService` to verify error handling
    spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

    // Trigger component initialization (calls ngOnInit)
    component.ngOnInit();

    // Wait for asynchronous tasks to stabilize
    await fixture.whenStable();
    fixture.detectChanges(); // Trigger initial change detection


    // **Act: Simulate user interactions**

    // Simulate navigation to the detail view of a Sales Order 
    const selectElementDetails = fixture.nativeElement.querySelector('#salesOrderListToDeatil');
    selectElementDetails?.click();

    // **Assert: Verify navigation to detail view**
    expect(component.salesOrderListDisplay).toBeFalsy(); // List view should not be displayed
    expect(component.salesOrderDetailDisplay).toBeTruthy(); // Detail view should be displayed

    fixture.detectChanges();
    await fixture.whenStable();

    // Access the detail component instance for assertions
    const salesOrderDebugElement = fixture.debugElement.query(By.css('app-sales-order-detail'));
    const salesOrderComponent = salesOrderDebugElement?.componentInstance;

    // Assert: Verify the detail component's state
    expect(salesOrderComponent).toBeTruthy(); // Detail component should be rendered

    salesOrderComponent.salesOrderDetail = salesOrderDetail;
    salesOrderComponent.ProductResponseDataset(salesOrderDetail.product.probes);

    // Ensure all Angular component changes are detected and stable
    fixture.detectChanges();
    await fixture.whenStable();

    // Set initial values for serial numbers in the form group
    salesOrderComponent.formGroup.get('serialNumberList')['controls'][0].get('serialNumber').setValue('T1A-123-45');
    salesOrderComponent.formGroup.get('serialNumberList')['controls'][1].get('serialNumber').setValue('T1A-123-45');

    // Simulate button clicks to add serial numbers
    const editSerialNumberButton = fixture.nativeElement.querySelectorAll('#addSalesOrderBtn');
    editSerialNumberButton[0].click();
    editSerialNumberButton[1].click();

    // Detect changes and wait for the component to stabilize
    fixture.detectChanges();
    await fixture.whenStable();

    // Close all serial number edits using the close button
    const salesOrderDetailCloseAllSarialNumberBtn = fixture.nativeElement.querySelector('#closeAllSalesOrderEdit');
    salesOrderDetailCloseAllSarialNumberBtn.click();

    // Detect changes and wait for the component to stabilize
    fixture.detectChanges();
    await fixture.whenStable();

    // Set the same serial number values again
    salesOrderComponent.formGroup.get('serialNumberList')['controls'][0].get('serialNumber').setValue('T1A-123-45');
    salesOrderComponent.formGroup.get('serialNumberList')['controls'][1].get('serialNumber').setValue('T1A-123-45');

    // Simulate button clicks to edit serial numbers again
    const editSerialNumberAgainButton = fixture.nativeElement.querySelectorAll('#addSalesOrderBtn');
    editSerialNumberAgainButton[0].click();
    editSerialNumberAgainButton[1].click();

    // Detect changes and wait for the component to stabilize
    fixture.detectChanges();
    await fixture.whenStable();

    // Locate all input elements with formControlName="serialNumber"
    const inputElements: NodeListOf<HTMLInputElement> = fixture.nativeElement.querySelectorAll(
      'input[formControlName="serialNumber"]'
    );

    // Trigger input events for each input element to simulate user input
    inputElements[0].dispatchEvent(new Event('input'));
    inputElements[1].dispatchEvent(new Event('input'));

    // Detect changes and wait for the component to stabilize
    fixture.detectChanges();
    await fixture.whenStable();

    // Verify if serial number already exists by checking the DOM element
    const SalesOrderDetailserialNumberExists = fixture.nativeElement.querySelector('#SalesOrderDetailserialNumberExists');
    expect(SalesOrderDetailserialNumberExists.textContent.trim()).toBe(salesOrderComponent.serialNumberExists);

    // Test setting invalid serial numbers and simulate input events
    salesOrderComponent.formGroup.get('serialNumberList')['controls'][0].get('serialNumber').setValue('T1A-123-45 `');
    salesOrderComponent.formGroup.get('serialNumberList')['controls'][1].get('serialNumber').setValue('T1A-123-54');
    inputElements[0].dispatchEvent(new Event('input'));
    inputElements[1].dispatchEvent(new Event('input'));

    // Detect changes and wait for the component to stabilize
    fixture.detectChanges();
    await fixture.whenStable();

    // Close a specific serial number edit
    const salesOrderDetailCloseSarialNumberBtn = fixture.nativeElement.querySelectorAll('#salesOrderDetailCloseSarialNumber');
    salesOrderDetailCloseSarialNumberBtn[1].click();

    // Attempt to save an invalid serial number and verify toast message
    const saveOneSalesOrderBtn = fixture.nativeElement.querySelector('#saveOneSalesOrderBtn');
    saveOneSalesOrderBtn.click();
    expect(toastrServiceMock.info).toHaveBeenCalledWith('Invalid Serial Number');

    // Set a valid serial number and try saving again
    salesOrderComponent.formGroup.get('serialNumberList')['controls'][0].get('serialNumber').setValue('T1A-123-45');
    inputElements[0].dispatchEvent(new Event('input'));
    saveOneSalesOrderBtn.click();

    // Detect changes and wait for the component to stabilize
    fixture.detectChanges();
    await fixture.whenStable();

    // Verify success toast message
    expect(toastrServiceMock.success).toHaveBeenCalledWith(ProbeSuccessMessage);

    // Update component state with sales order details and product dataset
    salesOrderComponent.salesOrderDetail = salesOrderDetail;
    salesOrderComponent.ProductResponseDataset(salesOrderDetail.product.probes);

    // Detect changes and wait for the component to stabilize
    fixture.detectChanges();
    await fixture.whenStable();

    // Update a serial number and simulate editing
    salesOrderComponent.formGroup.get('serialNumberList')['controls'][1].get('serialNumber').setValue('T1A-163-45');
    const editSerialNumberAgain1Button = fixture.nativeElement.querySelectorAll('#addSalesOrderBtn');
    editSerialNumberAgain1Button[1].click();

    // Detect changes and wait for the component to stabilize
    fixture.detectChanges();
    await fixture.whenStable();

    // Simulate saving all serial numbers with an invalid form state
    salesOrderComponent.formIsValid = false;
    const saveAllSerialNumberButton = fixture.nativeElement.querySelector('#salesOrderDetailSaveAllSarialNumber');
    saveAllSerialNumberButton.click();
    expect(toastrServiceMock.info).toHaveBeenCalledWith('Invalid Serial Number');

    // Set form state as valid and save all serial numbers again
    salesOrderComponent.formIsValid = true;
    saveAllSerialNumberButton.click();

    // Update component state with sales order details and product dataset
    salesOrderComponent.salesOrderDetail = salesOrderDetail;
    salesOrderComponent.ProductResponseDataset(salesOrderDetail.product.probes);
    fixture.detectChanges();
    await fixture.whenStable();

    const salesOrderDetailToProbeDetailButton = fixture.nativeElement.querySelector('#salesOrderDetailToProbeDetail');
    salesOrderDetailToProbeDetailButton.click();

    expect(salesOrderComponent.probeDetailPageDiplay).toBeTruthy();

    fixture.detectChanges();
    await fixture.whenStable();

    const probeDetailDebugElement = fixture.debugElement.query(By.css('app-ots-probes-detail'));
    expect(probeDetailDebugElement).not.toBeNull();
    const probeDetailComponent = probeDetailDebugElement.componentInstance;

    probeDetailComponent.back();

    fixture.detectChanges();
    await fixture.whenStable();

    expect(salesOrderComponent.salesOrderDetailPageDiplay).toBeTruthy();
    salesOrderComponent.bridgesResponse = salesOrderDetail.product.bridges;
    fixture.detectChanges();
    await fixture.whenStable();

    const salesOrderDetailToDeviceDetailButton = fixture.nativeElement.querySelector('#salesorderDetailToDeviceDetail');
    salesOrderDetailToDeviceDetailButton.click();

    expect(salesOrderComponent.deviceDetailPageDiplay).toBeTruthy();


    fixture.detectChanges();
    await fixture.whenStable();

    const deviceDetailDebugElement = fixture.debugElement.query(By.css('app-device-detail'));
    expect(deviceDetailDebugElement).not.toBeNull();
    const deviceDetailComponent = deviceDetailDebugElement.componentInstance;

    deviceDetailComponent.back();
    salesOrderComponent.bridgesResponse = salesOrderDetail.product.bridges;
    fixture.detectChanges();
    await fixture.whenStable();

    const resetBridgeBtn = fixture.nativeElement.querySelector('#resetBridge');
    resetBridgeBtn.click();

    component.isFilterHidden = true;
    // Spy on the `deleteSalesOrder` method to confirm it is called
    spyOn(salesOrderComponent, 'deleteSalesOrder').and.callThrough();

    await selectOperationOption(fixture, 1, '#salesOrderDetailOperation');

    fixture.detectChanges();
    await fixture.whenStable();

    // Assert: Verify the delete operation is triggered
    expect(salesOrderComponent.deleteSalesOrder).toHaveBeenCalled();

    // Assert: Confirm success message is displayed after deletion
    expect(toastrServiceMock.success).toHaveBeenCalledWith('sales order deleted successfully.');

    // **Assert: Verify navigation back to the listing page**
    expect(component.listPageRefreshForbackToDetailPage).toBeTruthy(); // Ensure list page is refreshed
    expect(component.salesOrderListDisplay).toBeTruthy(); // List view should be displayed
    expect(component.isFilterComponentInitWithApicall).toBeFalsy(); // Filter component should not be initialized
    expect(component.salesOrderDetailDisplay).toBeFalsy(); // Detail view should not be displayed
    expect(component.selectedSalesOrderIdList.length).toEqual(0); // No items should remain selected

  });

  it('should display sync sales order list when isSyncSalesOrderDisplay is true', () => {
    // Act
    component.hideShowSyncAndErrorListing(true);

    // Assert
    expect(component.salesOrderListDisplay).toBeTrue();  // Check that salesOrderListDisplay is true
    expect(component.salesOrderErrorListDisplay).toBeFalse();  // Check that salesOrderErrorListDisplay is false
  });

  it('should display error sales order list when isSyncSalesOrderDisplay is false', () => {
    // Act
    component.hideShowSyncAndErrorListing(false);

    // Assert
    expect(component.salesOrderListDisplay).toBeFalse();  // Check that salesOrderListDisplay is false
    expect(component.salesOrderErrorListDisplay).toBeTrue();  // Check that salesOrderErrorListDisplay is true
  });

  it("should display search results on list", async () => {
    // Arrange: Set up spies and mocks
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Simulate user permissions for accessing Sales Order functionality
    permissionServiceSpy.getSalesOrderPermission?.and.returnValue(true);

    // Mock API call for fetching Sales Order  List with a successful response
    salesOrderApiCallServicespy.getSalesOrderList?.and.returnValue(of(new HttpResponse<SalesOrderPageResponse>({
      body: salesOrderListResponse,
      status: 200,
      statusText: 'OK',
    })));

    // Mock API call for fetching Sales Order Save SerialNumber  with a successful response
    probeApiCallServiceSpy.saveMutiprobe?.and.returnValue(of(new HttpResponse<AddUpdateMultiProbeResponse>({
      body: saveSerialNumberErrorResponse,
      status: 200,
      statusText: 'OK',
    })));

    // Mock API call for fetching Sales Order  Details with a successful response
    salesOrderApiCallServicespy.getSalesOrderDetails?.and.returnValue(
      of(new HttpResponse<SalesOrderDetailResponse>({
        body: salesorderDetailResponse,
        status: 200,
        statusText: 'OK',
      }))
    );

    spyOn(salesOrderServiceSpy, 'callSalesOrderFailedListFilterRequestParameterSubject')?.and.callThrough();
    component.salesOrderSearchRequestBody = {
      countryIds: null,
      customerEmail: null,
      customerName: null,
      orderType: null,
      poNumber: null,
      productConfigStatus: [ProductConfigStatus.CONFIGURED],
      salesOrderNumber: "00001234",
      orderRecordType: null
    };
    // Spy on the `loadAll` method
    const loadAllSpy = spyOn(component, 'loadAll').and.callThrough();
    // Act: Initialize component and simulate actions
    component.ngOnInit();
    fixture.detectChanges();
    const filterComponent = fixture.debugElement.query(By.directive(SalesOrderFilterComponent)).componentInstance;
    expect(filterComponent).toBeTruthy();

    filterComponent.filterSalesOrderForm?.get('salesOrderNumber').setValue("00001234");
    const searchBtn = fixture.nativeElement.querySelector('#salesOrderFilterSearch');
    searchBtn?.click();

    fixture.detectChanges(); // Finalize change detection
    await fixture.whenStable(); // Wait for asynchronous tasks
    // Assert: Validate the last call to `loadAll`
    const lastCallArgs = loadAllSpy.calls.mostRecent()?.args;
    expect(lastCallArgs[0]).toEqual(jasmine.objectContaining({ salesOrderNumber: "00001234" }));
    // Ensure the form value matches the set value
    expect(filterComponent.filterSalesOrderForm?.get('salesOrderNumber').value).toEqual("00001234");

    // Simulate navigation to the detail view of a Sales Order 
    const selectElementDetails = fixture.nativeElement.querySelector('#salesOrderListToDeatil');
    selectElementDetails?.click();

    // **Assert: Verify navigation to detail view**
    expect(component.salesOrderListDisplay).toBeFalsy(); // List view should not be displayed
    expect(component.salesOrderDetailDisplay).toBeTruthy(); // Detail view should be displayed

    fixture.detectChanges();
    await fixture.whenStable();

    // Access the detail component instance for assertions
    const salesOrderDebugElement = fixture.debugElement.query(By.css('app-sales-order-detail'));
    const salesOrderComponent = salesOrderDebugElement?.componentInstance;

    // Assert: Verify the detail component's state
    expect(salesOrderComponent).toBeTruthy(); // Detail component should be rendered

    salesOrderComponent.salesOrderDetail = salesOrderDetail;
    salesOrderComponent.ProductResponseDataset(salesOrderDetail.product.probes);

    // Ensure all Angular component changes are detected and stable
    fixture.detectChanges();
    await fixture.whenStable();

    // Simulate button clicks to add serial numbers
    const editSerialNumberButton = fixture.nativeElement.querySelectorAll('#addSalesOrderBtn');
    editSerialNumberButton[0].click();

    // Detect changes and wait for the component to stabilize
    fixture.detectChanges();
    await fixture.whenStable();

    // Locate all input elements with formControlName="serialNumber"
    const inputElements: NodeListOf<HTMLInputElement> = fixture.nativeElement.querySelectorAll(
      'input[formControlName="serialNumber"]'
    );

    // Detect changes and wait for the component to stabilize
    fixture.detectChanges();
    await fixture.whenStable();

    // Attempt to save an invalid serial number and verify toast message
    const saveOneSalesOrderBtn = fixture.nativeElement.querySelector('#saveOneSalesOrderBtn');

    // Set a valid serial number and try saving again
    salesOrderComponent.formGroup.get('serialNumberList')['controls'][0].get('serialNumber').setValue('T1A-123-45');
    inputElements[0].dispatchEvent(new Event('input'));
    saveOneSalesOrderBtn.click();

    // Detect changes and wait for the component to stabilize
    fixture.detectChanges();
    await fixture.whenStable();
    // Verify if serial number already exists by checking the DOM element
    expect(salesOrderComponent.relatedProductList[0].apiErrorMessage).toBe("Serial number should start with T1B.");
  });

});
